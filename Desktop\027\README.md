# نظام صرف رواتب العمالة المنزلية

نظام شامل لإدارة بيانات العمالة المنزلية وصرف رواتبهم الشهرية مكتوب بلغة Python باستخدام إطار Flask.

## المميزات الرئيسية

### 🔐 نظام تسجيل الدخول
- تسجيل دخول آمن للمستخدمين
- جلسات محمية
- إدارة المستخدمين

### 👥 إدارة العمال
- إضافة وتعديل وحذف بيانات العمال
- تتبع معلومات العامل (الاسم، الجنسية، نوع العمل، رقم الهوية، إلخ)
- إدارة حالة العامل (نشط، غير نشط، منتهي الخدمة)

### 💰 إدارة الرواتب
- إضافة وتعديل رواتب العمال الشهرية
- حساب صافي الراتب تلقائياً (الراتب الأساسي + البدلات - الخصومات)
- تتبع حالة الدفع (مدفوع/غير مدفوع)
- إدارة تواريخ الدفع

### 📊 التقارير والإحصائيات
- لوحة تحكم شاملة مع الإحصائيات
- تصدير التقارير بصيغة PDF و Excel
- طباعة إيصالات الرواتب
- تقارير شهرية مفصلة

### 🎨 واجهة المستخدم
- تصميم عربي متجاوب (RTL)
- واجهة سهلة الاستخدام
- متوافق مع الأجهزة المحمولة

## متطلبات النظام

- Python 3.7 أو أحدث
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
# إذا كان لديك git
git clone [repository-url]
cd payroll-system

# أو قم بتحميل الملفات مباشرة
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python run.py
```

أو

```bash
python app.py
```

### 4. الوصول للنظام
افتح المتصفح وانتقل إلى:
- محلياً: http://localhost:5000
- من أجهزة أخرى في الشبكة: http://[IP-ADDRESS]:5000

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** قم بتغيير كلمة المرور الافتراضية بعد أول تسجيل دخول.

## هيكل المشروع

```
payroll-system/
├── app.py              # الملف الرئيسي للتطبيق
├── run.py              # ملف تشغيل النظام
├── models.py           # نماذج قاعدة البيانات
├── forms.py            # نماذج الويب
├── config.py           # إعدادات التطبيق
├── requirements.txt    # المكتبات المطلوبة
├── templates/          # قوالب HTML
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   ├── workers.html
│   ├── worker_form.html
│   ├── salaries.html
│   ├── salary_form.html
│   └── reports.html
├── static/             # ملفات CSS وJS
└── payroll.db          # قاعدة البيانات (تُنشأ تلقائياً)
```

## الاستخدام

### إدارة العمال
1. انتقل إلى "إدارة العمال"
2. اضغط "إضافة عامل جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### إدارة الرواتب
1. انتقل إلى "إدارة الرواتب"
2. اضغط "إضافة راتب جديد"
3. اختر العامل والشهر
4. أدخل تفاصيل الراتب
5. سيتم حساب صافي الراتب تلقائياً

### التقارير
1. انتقل إلى "التقارير"
2. اختر معايير التقرير (الشهر، السنة، حالة الدفع)
3. اضغط "معاينة التقرير" لعرض البيانات
4. اضغط "تصدير PDF" أو "تصدير Excel" لتحميل التقرير

### طباعة الإيصالات
1. من صفحة الرواتب، اضغط على أيقونة الطباعة بجانب الراتب
2. سيتم فتح إيصال PDF جاهز للطباعة

## الإعدادات

يمكن تخصيص الإعدادات في ملف `config.py`:

- اسم الشركة
- إعدادات قاعدة البيانات
- إعدادات الأمان
- إعدادات التقارير

## الأمان

- كلمات المرور محمية بالتشفير
- جلسات آمنة
- حماية من الوصول غير المصرح به
- التحقق من صحة البيانات

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملف README
2. راجع رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المكتبات المطلوبة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة:** هذا النظام مصمم للاستخدام المحلي أو في الشبكات الداخلية. للاستخدام على الإنترنت، يُنصح بإضافة طبقات أمان إضافية.
