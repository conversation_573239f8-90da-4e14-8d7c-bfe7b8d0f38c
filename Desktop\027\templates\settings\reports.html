{% extends "base.html" %}

{% block title %}إعدادات التقارير - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-file-pdf me-2"></i>إعدادات التقارير</h2>
            <p class="text-muted mb-0">تخصيص شكل ومحتوى التقارير وملفات PDF</p>
        </div>
        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للإعدادات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <!-- إعدادات PDF -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf me-2"></i>
                        إعدادات PDF
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.pdf_page_size.label(class="form-label") }}
                            {{ form.pdf_page_size(class="form-select") }}
                            {% if form.pdf_page_size.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pdf_page_size.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.pdf_orientation.label(class="form-label") }}
                            {{ form.pdf_orientation(class="form-select") }}
                            {% if form.pdf_orientation.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pdf_orientation.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.pdf_font_size.label(class="form-label") }}
                            {{ form.pdf_font_size(class="form-select") }}
                            {% if form.pdf_font_size.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.pdf_font_size.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.header_color.label(class="form-label") }}
                            {{ form.header_color(class="form-select") }}
                            {% if form.header_color.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.header_color.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إعدادات المحتوى -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        إعدادات المحتوى
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.include_company_logo.label(class="form-label") }}
                            {{ form.include_company_logo(class="form-select") }}
                            {% if form.include_company_logo.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.include_company_logo.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">إظهار شعار الشركة في أعلى التقرير</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.include_summary.label(class="form-label") }}
                            {{ form.include_summary(class="form-select") }}
                            {% if form.include_summary.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.include_summary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">إظهار ملخص الإحصائيات في نهاية التقرير</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.include_footer.label(class="form-label") }}
                            {{ form.include_footer(class="form-select") }}
                            {% if form.include_footer.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.include_footer.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">إظهار ملاحظة في أسفل التقرير</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        <div>
                            <button type="button" class="btn btn-outline-warning me-2" onclick="confirmReset('reports')">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                            
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-info me-2">
                                <i class="fas fa-eye me-2"></i>
                                معاينة التقرير
                            </a>
                            
                            {{ form.submit(class="btn btn-success") }}
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- معلومات مساعدة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مساعدة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <h6>حجم الصفحة:</h6>
                    <p>A4 للاستخدام العادي، A3 للتقارير الكبيرة</p>
                    
                    <h6>اتجاه الصفحة:</h6>
                    <p>أفقي لاستيعاب أعمدة أكثر، عمودي للتقارير البسيطة</p>
                    
                    <h6>حجم الخط:</h6>
                    <p>10 للاستخدام العادي، 8 لتوفير المساحة</p>
                    
                    <h6>لون الرأس:</h6>
                    <p>يؤثر على لون رأس الجدول في التقرير</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-palette me-2"></i>
                    معاينة الألوان
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <div class="p-2 text-white" style="background-color: darkblue;">أزرق داكن</div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="p-2 text-white" style="background-color: darkgreen;">أخضر داكن</div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="p-2 text-white" style="background-color: darkred;">أحمر داكن</div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="p-2 text-white" style="background-color: purple;">بنفسجي</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>استخدم الاتجاه الأفقي للتقارير الشاملة</li>
                    <li>اختر حجم خط مناسب للطباعة</li>
                    <li>تأكد من تضمين ملخص الإحصائيات</li>
                    <li>استخدم ألوان متناسقة مع هوية الشركة</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين إعدادات التقارير للقيم الافتراضية؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم فقدان جميع الإعدادات المخصصة
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('reset_settings') }}" style="display: inline;">
                    <input type="hidden" name="category" value="reports">
                    <button type="submit" class="btn btn-warning">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReset(category) {
    new bootstrap.Modal(document.getElementById('resetModal')).show();
}
</script>
{% endblock %}
