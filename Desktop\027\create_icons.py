#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء أيقونات احترافية لنظام صرف رواتب العمالة المنزلية
"""

import os
from PIL import Image, ImageDraw, ImageFont
import base64
from io import BytesIO

def create_app_icon():
    """إنشاء أيقونة التطبيق الرئيسية"""
    
    # إنشاء صورة 256x256 بخلفية زرقاء متدرجة
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم خلفية دائرية متدرجة
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color = (41, 128, 185, alpha)  # أزرق احترافي
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # رسم أيقونة المال والراتب
    # رسم مستطيل يمثل الراتب
    rect_width = size // 3
    rect_height = size // 4
    rect_x = (size - rect_width) // 2
    rect_y = (size - rect_height) // 2 - 20
    
    # خلفية المستطيل
    draw.rectangle([rect_x, rect_y, rect_x + rect_width, rect_y + rect_height], 
                  fill=(46, 204, 113, 255), outline=(39, 174, 96, 255), width=3)
    
    # رسم رمز العملة QR
    try:
        # محاولة استخدام خط عربي
        font_size = size // 12
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    # رسم نص QR في المستطيل
    text = "QR"
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = rect_x + (rect_width - text_width) // 2
    text_y = rect_y + (rect_height - text_height) // 2
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # رسم خطوط تمثل قائمة الرواتب
    line_y_start = rect_y + rect_height + 30
    line_spacing = 15
    line_width = rect_width + 40
    line_x = rect_x - 20
    
    for i in range(3):
        y = line_y_start + (i * line_spacing)
        # خط رئيسي
        draw.rectangle([line_x, y, line_x + line_width, y + 3], 
                      fill=(52, 73, 94, 200))
        # نقطة في البداية
        draw.ellipse([line_x - 8, y - 2, line_x + 2, y + 8], 
                    fill=(231, 76, 60, 255))
    
    # حفظ الأيقونة بأحجام مختلفة
    sizes = [16, 32, 48, 64, 128, 256]
    icon_path = "static/app_icon.ico"
    
    # إنشاء مجلد static إذا لم يكن موجود
    os.makedirs("static", exist_ok=True)
    
    # حفظ كـ ICO مع أحجام متعددة
    icon_images = []
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icon_images.append(resized)
    
    # حفظ ملف ICO
    icon_images[0].save(icon_path, format='ICO', sizes=[(s, s) for s in sizes])
    print(f"✅ تم إنشاء أيقونة التطبيق: {icon_path}")
    
    # حفظ كـ PNG أيضاً
    img.save("static/app_icon.png", format='PNG')
    print(f"✅ تم إنشاء أيقونة PNG: static/app_icon.png")
    
    return icon_path

def create_installer_icon():
    """إنشاء أيقونة المثبت"""
    
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية خضراء للمثبت
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color = (46, 204, 113, alpha)  # أخضر
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # رسم صندوق التثبيت
    box_size = size // 2
    box_x = (size - box_size) // 2
    box_y = (size - box_size) // 2
    
    # الصندوق
    draw.rectangle([box_x, box_y, box_x + box_size, box_y + box_size], 
                  fill=(39, 174, 96, 255), outline=(34, 153, 84, 255), width=4)
    
    # سهم التحميل
    arrow_size = box_size // 3
    arrow_x = box_x + (box_size - arrow_size) // 2
    arrow_y = box_y + (box_size - arrow_size) // 2
    
    # رسم السهم
    points = [
        (arrow_x + arrow_size//2, arrow_y),  # أعلى
        (arrow_x + arrow_size, arrow_y + arrow_size//2),  # يمين
        (arrow_x + arrow_size*3//4, arrow_y + arrow_size//2),  # يمين داخلي
        (arrow_x + arrow_size*3//4, arrow_y + arrow_size),  # أسفل يمين
        (arrow_x + arrow_size//4, arrow_y + arrow_size),  # أسفل يسار
        (arrow_x + arrow_size//4, arrow_y + arrow_size//2),  # يسار داخلي
        (arrow_x, arrow_y + arrow_size//2),  # يسار
    ]
    draw.polygon(points, fill=(255, 255, 255, 255))
    
    # حفظ الأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    icon_images = []
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icon_images.append(resized)
    
    icon_images[0].save("static/installer_icon.ico", format='ICO', sizes=[(s, s) for s in sizes])
    img.save("static/installer_icon.png", format='PNG')
    print(f"✅ تم إنشاء أيقونة المثبت: static/installer_icon.ico")

def create_portable_icon():
    """إنشاء أيقونة النسخة المحمولة"""
    
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية برتقالية للنسخة المحمولة
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color = (230, 126, 34, alpha)  # برتقالي
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # رسم حقيبة محمولة
    bag_width = size // 2
    bag_height = size // 3
    bag_x = (size - bag_width) // 2
    bag_y = (size - bag_height) // 2 + 20
    
    # الحقيبة
    draw.rectangle([bag_x, bag_y, bag_x + bag_width, bag_y + bag_height], 
                  fill=(211, 84, 0, 255), outline=(186, 74, 0, 255), width=3)
    
    # المقبض
    handle_width = bag_width // 3
    handle_x = bag_x + (bag_width - handle_width) // 2
    handle_y = bag_y - 20
    draw.rectangle([handle_x, handle_y, handle_x + handle_width, handle_y + 10], 
                  fill=(186, 74, 0, 255))
    
    # رسم شاشة صغيرة داخل الحقيبة
    screen_size = bag_width // 2
    screen_x = bag_x + (bag_width - screen_size) // 2
    screen_y = bag_y + 15
    draw.rectangle([screen_x, screen_y, screen_x + screen_size, screen_y + screen_size//2], 
                  fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=2)
    
    # حفظ الأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    icon_images = []
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icon_images.append(resized)
    
    icon_images[0].save("static/portable_icon.ico", format='ICO', sizes=[(s, s) for s in sizes])
    img.save("static/portable_icon.png", format='PNG')
    print(f"✅ تم إنشاء أيقونة النسخة المحمولة: static/portable_icon.ico")

def create_usb_icon():
    """إنشاء أيقونة USB"""
    
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية بنفسجية لـ USB
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color = (155, 89, 182, alpha)  # بنفسجي
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # رسم فلاشة USB
    usb_width = size // 3
    usb_height = size // 2
    usb_x = (size - usb_width) // 2
    usb_y = (size - usb_height) // 2
    
    # جسم الفلاشة
    draw.rectangle([usb_x, usb_y, usb_x + usb_width, usb_y + usb_height], 
                  fill=(142, 68, 173, 255), outline=(125, 60, 152, 255), width=3)
    
    # الموصل
    connector_width = usb_width // 2
    connector_height = 20
    connector_x = usb_x + (usb_width - connector_width) // 2
    connector_y = usb_y + usb_height
    draw.rectangle([connector_x, connector_y, connector_x + connector_width, connector_y + connector_height], 
                  fill=(125, 60, 152, 255))
    
    # LED صغير
    led_size = 8
    led_x = usb_x + usb_width - led_size - 10
    led_y = usb_y + 10
    draw.ellipse([led_x, led_y, led_x + led_size, led_y + led_size], 
                fill=(46, 204, 113, 255))
    
    # حفظ الأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    icon_images = []
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icon_images.append(resized)
    
    icon_images[0].save("static/usb_icon.ico", format='ICO', sizes=[(s, s) for s in sizes])
    img.save("static/usb_icon.png", format='PNG')
    print(f"✅ تم إنشاء أيقونة USB: static/usb_icon.ico")

def create_service_icon():
    """إنشاء أيقونة الخدمة"""
    
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # خلفية زرقاء داكنة للخدمة
    for i in range(size//2):
        alpha = int(255 * (1 - i / (size//2)))
        color = (52, 73, 94, alpha)  # أزرق داكن
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # رسم خادم/شبكة
    server_width = size // 2
    server_height = size // 4
    server_x = (size - server_width) // 2
    
    # رسم 3 خوادم مكدسة
    for i in range(3):
        server_y = (size // 2) - (server_height * 3 // 2) + (i * (server_height + 10))
        
        # الخادم
        draw.rectangle([server_x, server_y, server_x + server_width, server_y + server_height], 
                      fill=(44, 62, 80, 255), outline=(52, 73, 94, 255), width=2)
        
        # LED الحالة
        led_size = 6
        led_x = server_x + 10
        led_y = server_y + (server_height - led_size) // 2
        color = (46, 204, 113, 255) if i == 1 else (149, 165, 166, 255)
        draw.ellipse([led_x, led_y, led_x + led_size, led_y + led_size], fill=color)
        
        # خطوط تمثل البيانات
        for j in range(3):
            line_x = server_x + 30 + (j * 15)
            line_y = server_y + 8
            line_height = server_height - 16
            draw.rectangle([line_x, line_y, line_x + 3, line_y + line_height], 
                          fill=(149, 165, 166, 255))
    
    # حفظ الأيقونة
    sizes = [16, 32, 48, 64, 128, 256]
    icon_images = []
    for icon_size in sizes:
        resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icon_images.append(resized)
    
    icon_images[0].save("static/service_icon.ico", format='ICO', sizes=[(s, s) for s in sizes])
    img.save("static/service_icon.png", format='PNG')
    print(f"✅ تم إنشاء أيقونة الخدمة: static/service_icon.ico")

def main():
    """إنشاء جميع الأيقونات"""
    print("🎨 بدء إنشاء الأيقونات الاحترافية...")
    
    try:
        create_app_icon()
        create_installer_icon()
        create_portable_icon()
        create_usb_icon()
        create_service_icon()
        
        print("\n✅ تم إنشاء جميع الأيقونات بنجاح!")
        print("📁 الأيقونات محفوظة في مجلد: static/")
        print("🎯 يمكن الآن استخدام الأيقونات في جميع ملفات التثبيت")
        
    except ImportError:
        print("❌ مكتبة PIL غير مثبتة")
        print("📦 لتثبيتها: pip install Pillow")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونات: {e}")

if __name__ == "__main__":
    main()
