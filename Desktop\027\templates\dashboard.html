{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h2>
            <p class="text-muted mb-0">نظرة عامة على النظام</p>
        </div>
        <div class="text-end">
            <small class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                {{ current_date.strftime('%Y/%m/%d') }}
            </small>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ stats.total_workers }}</h3>
                    <p class="mb-0">إجمالي العمال</p>
                </div>
                <i class="fas fa-users fa-2x opacity-75"></i>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ stats.active_workers }}</h3>
                    <p class="mb-0">العمال النشطون</p>
                </div>
                <i class="fas fa-user-check fa-2x opacity-75"></i>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ stats.current_month_salaries }}</h3>
                    <p class="mb-0">رواتب هذا الشهر</p>
                </div>
                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1">{{ stats.unpaid_salaries }}</h3>
                    <p class="mb-0">رواتب غير مدفوعة</p>
                </div>
                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Activities -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر الأنشطة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_salaries %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>العامل</th>
                                    <th>الشهر</th>
                                    <th>صافي الراتب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in recent_salaries %}
                                <tr>
                                    <td>
                                        <strong>{{ salary.worker.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ salary.worker.job_type }}</small>
                                    </td>
                                    <td>{{ salary.get_month_name() }} {{ salary.year }}</td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            {{ "{:,.0f}".format(salary.net_salary) }} ر.ق
                                        </span>
                                    </td>
                                    <td>
                                        {% if salary.payment_status == 'paid' %}
                                            <span class="badge bg-success">مدفوع</span>
                                        {% else %}
                                            <span class="badge bg-warning">غير مدفوع</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ salary.created_at.strftime('%Y/%m/%d') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أنشطة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عامل جديد
                    </a>

                    <a href="{{ url_for('add_salary') }}" class="btn btn-success">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إضافة راتب
                    </a>

                    <a href="{{ url_for('reports') }}" class="btn btn-info">
                        <i class="fas fa-file-pdf me-2"></i>
                        تصدير تقرير
                    </a>

                    <hr>

                    <div class="text-center">
                        <h6 class="text-muted">إجمالي الرواتب هذا الشهر</h6>
                        <h4 class="text-primary">
                            {{ "{:,.0f}".format(stats.total_monthly_amount) }} ر.ق
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Summary Chart -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    ملخص الرواتب الشهرية
                </h5>
            </div>
            <div class="card-body">
                {% if monthly_summary %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>الشهر</th>
                                    <th>عدد العمال</th>
                                    <th>إجمالي الرواتب</th>
                                    <th>المدفوع</th>
                                    <th>غير المدفوع</th>
                                    <th>النسبة المدفوعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for summary in monthly_summary %}
                                <tr>
                                    <td>{{ summary.month_name }} {{ summary.year }}</td>
                                    <td>{{ summary.worker_count }}</td>
                                    <td class="fw-bold">{{ "{:,.0f}".format(summary.total_amount) }} ر.ق</td>
                                    <td class="text-success">{{ "{:,.0f}".format(summary.paid_amount) }} ر.ق</td>
                                    <td class="text-danger">{{ "{:,.0f}".format(summary.unpaid_amount) }} ر.ق</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success"
                                                 style="width: {{ summary.paid_percentage }}%">
                                                {{ "{:.0f}".format(summary.paid_percentage) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات للعرض</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
