#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء ملف تثبيت احترافي لنظام صرف رواتب العمالة المنزلية
"""

import os
import shutil
from pathlib import Path

def create_installer_files():
    """إنشاء ملفات التثبيت"""
    
    # إنشاء مجلد التثبيت
    installer_dir = Path("installer")
    installer_dir.mkdir(exist_ok=True)
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        ("dist/PayrollSystem.exe", "installer/PayrollSystem.exe"),
        ("run_exe.bat", "installer/run_exe.bat"),
        ("README_STANDALONE.md", "installer/دليل_الاستخدام.md"),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ تم نسخ: {src} -> {dst}")
        else:
            print(f"❌ ملف غير موجود: {src}")
    
    # إنشاء ملف التثبيت التلقائي
    create_auto_installer()
    
    # إنشاء ملف إلغاء التثبيت
    create_uninstaller()
    
    print("✅ تم إنشاء ملفات التثبيت بنجاح!")

def create_auto_installer():
    """إنشاء ملف التثبيت التلقائي"""
    installer_script = '''@echo off
chcp 65001 >nul
title تثبيت نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🏠 تثبيت نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================

echo 📁 إنشاء مجلد التطبيق...
set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام الرواتب"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📦 نسخ ملفات التطبيق...
copy "PayrollSystem.exe" "%INSTALL_DIR%\\"
copy "run_exe.bat" "%INSTALL_DIR%\\"
copy "دليل_الاستخدام.md" "%INSTALL_DIR%\\"

echo 🔗 إنشاء اختصار على سطح المكتب...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\نظام الرواتب.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\PayrollSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\\PayrollSystem.exe'; $Shortcut.Save()"

echo 📋 إنشاء ملف إلغاء التثبيت...
echo @echo off > "%INSTALL_DIR%\\uninstall.bat"
echo echo حذف نظام الرواتب... >> "%INSTALL_DIR%\\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\\uninstall.bat"
echo del "%USERPROFILE%\\Desktop\\نظام الرواتب.lnk" >> "%INSTALL_DIR%\\uninstall.bat"
echo echo تم حذف النظام بنجاح! >> "%INSTALL_DIR%\\uninstall.bat"
echo pause >> "%INSTALL_DIR%\\uninstall.bat"

echo ✅ تم التثبيت بنجاح!
echo 🚀 يمكنك الآن تشغيل النظام من:
echo    - الاختصار على سطح المكتب
echo    - مجلد: %INSTALL_DIR%
echo ============================================================
echo 🌐 الرابط: http://localhost:5000
echo 🔑 المستخدم: admin / كلمة المرور: admin123
echo ============================================================
pause
'''
    
    with open("installer/install.bat", "w", encoding="utf-8") as f:
        f.write(installer_script)

def create_uninstaller():
    """إنشاء ملف إلغاء التثبيت"""
    uninstaller_script = '''@echo off
chcp 65001 >nul
title إلغاء تثبيت نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🗑️ إلغاء تثبيت نظام صرف رواتب العمالة المنزلية
echo ============================================================

set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام الرواتب"

echo ❓ هل تريد حذف النظام نهائياً؟ (Y/N)
set /p confirm=

if /i "%confirm%"=="Y" (
    echo 🗑️ حذف ملفات النظام...
    if exist "%INSTALL_DIR%" rd /s /q "%INSTALL_DIR%"
    
    echo 🔗 حذف الاختصار...
    if exist "%USERPROFILE%\\Desktop\\نظام الرواتب.lnk" del "%USERPROFILE%\\Desktop\\نظام الرواتب.lnk"
    
    echo ✅ تم حذف النظام بنجاح!
) else (
    echo ❌ تم إلغاء عملية الحذف
)

echo ============================================================
pause
'''
    
    with open("installer/uninstall.bat", "w", encoding="utf-8") as f:
        f.write(uninstaller_script)

if __name__ == "__main__":
    create_installer_files()
