@echo off
chcp 65001 >nul
title نظام صرف رواتب العمالة المنزلية - نسخة محمولة محسنة

cls
echo.
echo ============================================================
echo 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 📱 النسخة المحمولة المحسنة - الإصدار 13.1
echo ============================================================
echo.

echo 🔍 فحص النظام...

REM فحص وجود الملف التنفيذي
if not exist "PayrollSystem.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo 💡 تأكد من وجود PayrollSystem.exe في نفس المجلد
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود

REM فحص وجود قاعدة البيانات
if not exist "payroll.db" (
    echo ⚠️ قاعدة البيانات غير موجودة، سيتم إنشاؤها تلقائياً
)

echo.
echo 🚀 بدء تشغيل النظام...
echo ⏳ يرجى الانتظار 15-30 ثانية...
echo 🌐 سيفتح المتصفح على: http://localhost:5000
echo.

REM تشغيل النظام
start "" "PayrollSystem.exe"

REM انتظار تحميل النظام
echo ⏳ انتظار تحميل النظام...
timeout /t 20 /nobreak >nul

REM فتح المتصفح
echo 🌐 فتح المتصفح...
start "" "http://localhost:5000"

echo.
echo ============================================================
echo ✅ تم تشغيل النظام بنجاح!
echo 🔗 الرابط: http://localhost:5000
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo ============================================================
echo 💾 البيانات محفوظة في نفس المجلد
echo 🔄 يمكن نقل المجلد لأي مكان آخر
echo 🛑 لإيقاف النظام: أغلق نافذة المتصفح ثم اضغط Ctrl+C
echo ============================================================
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
