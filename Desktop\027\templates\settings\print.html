{% extends "base.html" %}

{% block title %}إعدادات الطباعة - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-print me-2"></i>إعدادات الطباعة</h2>
            <p class="text-muted mb-0">تخصيص إعدادات الطابعة وجودة الطباعة</p>
        </div>
        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للإعدادات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <!-- إعدادات الطابعة -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-printer me-2"></i>
                        إعدادات الطابعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.default_printer.label(class="form-label") }}
                            {{ form.default_printer(class="form-control") }}
                            {% if form.default_printer.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.default_printer.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">اتركه فارغاً لاستخدام الطابعة الافتراضية للنظام</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.print_quality.label(class="form-label") }}
                            {{ form.print_quality(class="form-select") }}
                            {% if form.print_quality.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.print_quality.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إعدادات الإيصالات -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        إعدادات الإيصالات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.receipt_copies.label(class="form-label") }}
                            {{ form.receipt_copies(class="form-select") }}
                            {% if form.receipt_copies.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.receipt_copies.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.receipt_paper_size.label(class="form-label") }}
                            {{ form.receipt_paper_size(class="form-select") }}
                            {% if form.receipt_paper_size.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.receipt_paper_size.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.auto_print_receipts.label(class="form-label") }}
                            {{ form.auto_print_receipts(class="form-select") }}
                            {% if form.auto_print_receipts.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.auto_print_receipts.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">طباعة الإيصال تلقائياً عند إضافة راتب جديد</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إعدادات التقارير -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        إعدادات التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.report_copies.label(class="form-label") }}
                            {{ form.report_copies(class="form-select") }}
                            {% if form.report_copies.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.report_copies.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إعدادات الهوامش -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-expand-arrows-alt me-2"></i>
                        إعدادات الهوامش
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.margin_top.label(class="form-label") }}
                            {{ form.margin_top(class="form-select") }}
                            {% if form.margin_top.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.margin_top.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.margin_bottom.label(class="form-label") }}
                            {{ form.margin_bottom(class="form-select") }}
                            {% if form.margin_bottom.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.margin_bottom.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.margin_left.label(class="form-label") }}
                            {{ form.margin_left(class="form-select") }}
                            {% if form.margin_left.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.margin_left.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.margin_right.label(class="form-label") }}
                            {{ form.margin_right(class="form-select") }}
                            {% if form.margin_right.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.margin_right.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        <div>
                            <button type="button" class="btn btn-outline-warning me-2" onclick="confirmReset('print')">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                            
                            <button type="button" class="btn btn-outline-info me-2" onclick="testPrint()">
                                <i class="fas fa-print me-2"></i>
                                اختبار الطباعة
                            </button>
                            
                            {{ form.submit(class="btn btn-info") }}
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- معلومات مساعدة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مساعدة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <h6>الطابعة الافتراضية:</h6>
                    <p>اسم الطابعة كما يظهر في النظام</p>
                    
                    <h6>عدد النسخ:</h6>
                    <p>عدد النسخ المطبوعة لكل إيصال أو تقرير</p>
                    
                    <h6>حجم الورق:</h6>
                    <p>A4 للاستخدام العادي، الحراري للطابعات الحرارية</p>
                    
                    <h6>الهوامش:</h6>
                    <p>المسافة من حواف الورقة</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-print me-2"></i>
                    أحجام الورق
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>A4:</strong> 210 × 297 مم</p>
                    <p><strong>A5:</strong> 148 × 210 مم</p>
                    <p><strong>حراري 80 مم:</strong> للطابعات الحرارية</p>
                    <p><strong>حراري 58 مم:</strong> للطابعات الصغيرة</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>تأكد من تشغيل الطابعة قبل الطباعة</li>
                    <li>استخدم جودة عادية للاستخدام اليومي</li>
                    <li>اضبط الهوامش حسب نوع الطابعة</li>
                    <li>اختبر الطباعة بعد تغيير الإعدادات</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين إعدادات الطباعة للقيم الافتراضية؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم فقدان جميع الإعدادات المخصصة
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('reset_settings') }}" style="display: inline;">
                    <input type="hidden" name="category" value="print">
                    <button type="submit" class="btn btn-warning">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReset(category) {
    new bootstrap.Modal(document.getElementById('resetModal')).show();
}

function testPrint() {
    // يمكن إضافة وظيفة اختبار الطباعة لاحقاً
    alert('ميزة اختبار الطباعة ستكون متاحة قريباً');
}
</script>
{% endblock %}
