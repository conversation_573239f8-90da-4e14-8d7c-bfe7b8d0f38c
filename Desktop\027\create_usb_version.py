#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء نسخة USB لنظام صرف رواتب العمالة المنزلية
"""

import os
import shutil
from pathlib import Path

def create_usb_version():
    """إنشاء نسخة USB"""

    # إنشاء مجلد USB
    usb_dir = Path("USB_PayrollSystem")
    if usb_dir.exists():
        shutil.rmtree(usb_dir)
    usb_dir.mkdir()

    # نسخ الملفات الأساسية
    essential_files = [
        ("dist/PayrollSystem.exe", "USB_PayrollSystem/PayrollSystem.exe"),
        ("README_STANDALONE.md", "USB_PayrollSystem/دليل_الاستخدام.md"),
        ("static/usb_icon.ico", "USB_PayrollSystem/usb_icon.ico"),
        ("static/app_icon.ico", "USB_PayrollSystem/app_icon.ico"),
    ]

    for src, dst in essential_files:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ تم نسخ: {src}")

    # إنشاء ملفات USB
    create_usb_launcher()
    create_usb_installer()
    create_autorun_file()
    create_usb_guide()

    print("✅ تم إنشاء نسخة USB بنجاح!")

def create_usb_launcher():
    """إنشاء ملف تشغيل USB"""
    launcher_script = '''@echo off
chcp 65001 >nul
title نظام صرف رواتب العمالة المنزلية - نسخة USB

echo ============================================================
echo 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 💾 نسخة USB - تعمل من الفلاشة مباشرة
echo ============================================================

echo 🚀 بدء تشغيل النظام من USB...
echo ⏳ يرجى الانتظار 10-30 ثانية...
echo 🌐 سيفتح المتصفح على: http://localhost:5000

echo 📁 مجلد العمل: %~dp0
cd /d "%~dp0"

start PayrollSystem.exe

echo ✅ تم تشغيل النظام من USB!
echo 🔗 إذا لم يفتح المتصفح: http://localhost:5000
echo 🔑 بيانات الدخول: admin / admin123
echo ============================================================
echo 💾 البيانات محفوظة على الفلاشة
echo 🔄 يمكن استخدام النظام على أي جهاز
echo ============================================================
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''

    with open("USB_PayrollSystem/تشغيل_من_USB.bat", "w", encoding="utf-8") as f:
        f.write(launcher_script)

def create_usb_installer():
    """إنشاء ملف تثبيت من USB"""
    installer_script = '''@echo off
chcp 65001 >nul
title تثبيت النظام من USB إلى الجهاز

echo ============================================================
echo 📦 تثبيت نظام الرواتب من USB إلى الجهاز
echo ============================================================

echo ❓ هل تريد تثبيت النظام على هذا الجهاز؟ (Y/N)
set /p confirm=

if /i "%confirm%"=="Y" (
    echo 📁 إنشاء مجلد التطبيق...
    set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام الرواتب"
    if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

    echo 📦 نسخ ملفات التطبيق...
    copy "PayrollSystem.exe" "%INSTALL_DIR%\\"
    copy "دليل_الاستخدام.md" "%INSTALL_DIR%\\"

    echo 🔗 إنشاء اختصار على سطح المكتب...
    powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\نظام الرواتب.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\PayrollSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

    echo ✅ تم التثبيت بنجاح!
    echo 🚀 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 📁 مجلد التطبيق: %INSTALL_DIR%
) else (
    echo ❌ تم إلغاء التثبيت
)

echo ============================================================
pause
'''

    with open("USB_PayrollSystem/تثبيت_على_الجهاز.bat", "w", encoding="utf-8") as f:
        f.write(installer_script)

def create_autorun_file():
    """إنشاء ملف التشغيل التلقائي"""
    autorun_content = '''[autorun]
open=تشغيل_من_USB.bat
icon=usb_icon.ico
label=نظام صرف رواتب العمالة المنزلية
'''

    with open("USB_PayrollSystem/autorun.inf", "w", encoding="utf-8") as f:
        f.write(autorun_content)

def create_usb_guide():
    """إنشاء دليل USB"""
    guide_content = '''# 💾 دليل استخدام نسخة USB

## 🎯 مميزات نسخة USB:
- ✅ تعمل من الفلاشة مباشرة
- ✅ لا تحتاج تثبيت على الجهاز
- ✅ البيانات محفوظة على الفلاشة
- ✅ يمكن استخدامها على أي جهاز
- ✅ نسخ احتياطية تلقائية

## 🚀 طرق الاستخدام:

### 📱 التشغيل المباشر من USB:
1. أدخل الفلاشة في الجهاز
2. شغل "تشغيل_من_USB.bat"
3. انتظر حتى يفتح المتصفح
4. سجل الدخول: admin / admin123

### 📦 التثبيت على الجهاز:
1. شغل "تثبيت_على_الجهاز.bat"
2. اتبع التعليمات
3. سيتم إنشاء اختصار على سطح المكتب

### 🔄 التشغيل التلقائي:
- في بعض الأجهزة سيعمل تلقائياً عند إدخال الفلاشة
- إذا لم يعمل، شغل الملف يدوياً

## 💾 إدارة البيانات:

### 📁 موقع البيانات:
- ملف قاعدة البيانات: payroll.db (على الفلاشة)
- الإعدادات: محفوظة مع التطبيق
- التقارير: يمكن حفظها على الفلاشة

### 🔄 النسخ الاحتياطي:
- انسخ ملف payroll.db إلى مكان آمن
- أو انسخ مجلد USB بالكامل
- للاستعادة: استبدل الملف

## 🔒 الأمان:
- احم الفلاشة بكلمة مرور
- لا تتركها في أجهزة عامة
- اعمل نسخ احتياطية دورية
- غير كلمة مرور النظام الافتراضية

## 🌐 الشبكة:
- يمكن الوصول من أجهزة أخرى: http://[IP]:5000
- تأكد من إعدادات Firewall
- استخدم شبكة آمنة

## 🛠️ استكشاف الأخطاء:

### ❌ لا يعمل التشغيل التلقائي:
- شغل الملف يدوياً
- تحقق من إعدادات الأمان
- جرب تشغيل كمدير

### 🐌 بطء في التشغيل:
- استخدم فلاشة USB 3.0
- تأكد من مساحة كافية
- أغلق البرامج غير الضرورية

### 💾 مشكلة في حفظ البيانات:
- تحقق من مساحة الفلاشة
- تأكد من عدم حماية الكتابة
- جرب فلاشة أخرى

## 📊 متطلبات الفلاشة:
- الحجم: 256 MB على الأقل (1 GB موصى به)
- النوع: USB 2.0 أو أحدث
- نظام الملفات: FAT32 أو NTFS
- مساحة فارغة: 200 MB على الأقل

## 🎯 نصائح للاستخدام:
- استخدم فلاشة سريعة للأداء الأفضل
- اعمل نسخ احتياطية دورية
- لا تنزع الفلاشة أثناء عمل النظام
- استخدم "Safely Remove Hardware"

تاريخ الإنشاء: 29 مايو 2025
الإصدار: 13.0 - USB Portable Edition
'''

    with open("USB_PayrollSystem/دليل_USB.md", "w", encoding="utf-8") as f:
        f.write(guide_content)

if __name__ == "__main__":
    create_usb_version()
