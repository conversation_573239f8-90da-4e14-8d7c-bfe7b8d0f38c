from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='admin')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Worker(db.Model):
    """نموذج العمال"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    nationality = db.Column(db.String(50), nullable=False)
    job_type = db.Column(db.String(50), nullable=False)
    id_number = db.Column(db.String(20), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    status = db.Column(db.String(20), default='active')  # active, inactive, terminated
    hire_date = db.Column(db.Date, nullable=False)
    basic_salary = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # علاقة مع الرواتب
    salaries = db.relationship('Salary', backref='worker', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Worker {self.name}>'

class Salary(db.Model):
    """نموذج الرواتب"""
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('worker.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)  # 1-12
    year = db.Column(db.Integer, nullable=False)
    basic_salary = db.Column(db.Float, nullable=False, default=0.0)
    allowances = db.Column(db.Float, default=0.0)  # البدلات
    deductions = db.Column(db.Float, default=0.0)  # الخصومات
    net_salary = db.Column(db.Float, nullable=False)  # صافي الراتب
    payment_status = db.Column(db.String(20), default='unpaid')  # paid, unpaid
    payment_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # فهرس مركب لضمان عدم تكرار الراتب لنفس العامل في نفس الشهر
    __table_args__ = (db.UniqueConstraint('worker_id', 'month', 'year', name='unique_worker_month_year'),)

    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        self.net_salary = self.basic_salary + self.allowances - self.deductions
        return self.net_salary

    def get_month_name(self):
        """الحصول على اسم الشهر بالعربية"""
        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        return months.get(self.month, 'غير محدد')

    def __repr__(self):
        return f'<Salary {self.worker.name} - {self.get_month_name()} {self.year}>'

class Settings(db.Model):
    """نموذج الإعدادات"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=False)  # system, reports, print
    description = db.Column(db.String(200), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Settings {self.key}: {self.value}>'

    @staticmethod
    def get_setting(key, default=None):
        """الحصول على قيمة إعداد"""
        setting = Settings.query.filter_by(key=key).first()
        return setting.value if setting else default

    @staticmethod
    def set_setting(key, value, category='system', description=None):
        """تعيين قيمة إعداد"""
        setting = Settings.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            setting.updated_at = datetime.utcnow()
        else:
            setting = Settings(
                key=key,
                value=value,
                category=category,
                description=description
            )
            db.session.add(setting)
        db.session.commit()
        return setting

    @staticmethod
    def get_category_settings(category):
        """الحصول على جميع إعدادات فئة معينة"""
        return Settings.query.filter_by(category=category).all()

    @staticmethod
    def initialize_default_settings():
        """تهيئة الإعدادات الافتراضية"""
        default_settings = [
            # إعدادات النظام
            ('company_name', 'مؤسسة الخدمات المنزلية - دولة قطر', 'system', 'اسم الشركة'),
            ('company_address', 'الدوحة، دولة قطر', 'system', 'عنوان الشركة'),
            ('company_phone', '+974 1234 5678', 'system', 'هاتف الشركة'),
            ('company_email', '<EMAIL>', 'system', 'بريد الشركة'),
            ('company_website', 'www.company.qa', 'system', 'موقع الشركة'),
            ('currency_name', 'الريال القطري', 'system', 'اسم العملة'),
            ('currency_symbol', 'ر.ق', 'system', 'رمز العملة'),
            ('system_language', 'ar', 'system', 'لغة النظام'),
            ('timezone', 'Asia/Qatar', 'system', 'المنطقة الزمنية'),
            ('date_format', '%Y/%m/%d', 'system', 'تنسيق التاريخ'),
            ('enable_ticker', 'yes', 'system', 'تفعيل الشريط المتحرك'),
            ('ticker_speed', 'normal', 'system', 'سرعة الشريط المتحرك'),
            ('ticker_content', 'workers', 'system', 'محتوى الشريط المتحرك'),
            ('ticker_custom_text', 'مرحباً بكم في نظام صرف رواتب العمالة المنزلية - دولة قطر', 'system', 'النص المخصص للشريط'),

            # إعدادات التقارير
            ('pdf_page_size', 'A4', 'reports', 'حجم صفحة PDF'),
            ('pdf_orientation', 'landscape', 'reports', 'اتجاه صفحة PDF'),
            ('pdf_font_size', '10', 'reports', 'حجم خط PDF'),
            ('include_company_logo', 'yes', 'reports', 'تضمين شعار الشركة'),
            ('include_summary', 'yes', 'reports', 'تضمين ملخص الإحصائيات'),
            ('include_footer', 'yes', 'reports', 'تضمين الملاحظة السفلية'),
            ('header_color', 'darkblue', 'reports', 'لون رأس الجدول'),

            # إعدادات الطباعة
            ('default_printer', '', 'print', 'الطابعة الافتراضية'),
            ('receipt_copies', '1', 'print', 'عدد نسخ الإيصالات'),
            ('receipt_paper_size', 'A4', 'print', 'حجم ورق الإيصالات'),
            ('report_copies', '1', 'print', 'عدد نسخ التقارير'),
            ('auto_print_receipts', 'no', 'print', 'طباعة الإيصالات تلقائياً'),
            ('print_quality', 'normal', 'print', 'جودة الطباعة'),
            ('margin_top', '1.0', 'print', 'الهامش العلوي'),
            ('margin_bottom', '1.0', 'print', 'الهامش السفلي'),
            ('margin_left', '1.0', 'print', 'الهامش الأيسر'),
            ('margin_right', '1.0', 'print', 'الهامش الأيمن'),
        ]

        for key, value, category, description in default_settings:
            if not Settings.query.filter_by(key=key).first():
                Settings.set_setting(key, value, category, description)
