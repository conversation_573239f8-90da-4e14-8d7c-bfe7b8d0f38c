from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='admin')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Worker(db.Model):
    """نموذج العمال"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    nationality = db.Column(db.String(50), nullable=False)
    job_type = db.Column(db.String(50), nullable=False)
    id_number = db.Column(db.String(20), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    status = db.Column(db.String(20), default='active')  # active, inactive, terminated
    hire_date = db.Column(db.Date, nullable=False)
    basic_salary = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # علاقة مع الرواتب
    salaries = db.relationship('Salary', backref='worker', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Worker {self.name}>'

class Salary(db.Model):
    """نموذج الرواتب"""
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('worker.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)  # 1-12
    year = db.Column(db.Integer, nullable=False)
    basic_salary = db.Column(db.Float, nullable=False, default=0.0)
    allowances = db.Column(db.Float, default=0.0)  # البدلات
    deductions = db.Column(db.Float, default=0.0)  # الخصومات
    net_salary = db.Column(db.Float, nullable=False)  # صافي الراتب
    payment_status = db.Column(db.String(20), default='unpaid')  # paid, unpaid
    payment_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # فهرس مركب لضمان عدم تكرار الراتب لنفس العامل في نفس الشهر
    __table_args__ = (db.UniqueConstraint('worker_id', 'month', 'year', name='unique_worker_month_year'),)
    
    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        self.net_salary = self.basic_salary + self.allowances - self.deductions
        return self.net_salary
    
    def get_month_name(self):
        """الحصول على اسم الشهر بالعربية"""
        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        return months.get(self.month, 'غير محدد')
    
    def __repr__(self):
        return f'<Salary {self.worker.name} - {self.get_month_name()} {self.year}>'
