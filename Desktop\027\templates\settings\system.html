{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-desktop me-2"></i>إعدادات النظام</h2>
            <p class="text-muted mb-0">تخصيص معلومات الشركة وإعدادات النظام الأساسية</p>
        </div>
        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للإعدادات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <form method="POST">
            {{ form.hidden_tag() }}

            <!-- معلومات الشركة -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>
                        معلومات الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.company_name.label(class="form-label") }}
                            {{ form.company_name(class="form-control") }}
                            {% if form.company_name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.company_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-12 mb-3">
                            {{ form.company_address.label(class="form-label") }}
                            {{ form.company_address(class="form-control", rows="3") }}
                            {% if form.company_address.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.company_address.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.company_phone.label(class="form-label") }}
                            {{ form.company_phone(class="form-control") }}
                            {% if form.company_phone.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.company_phone.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.company_email.label(class="form-label") }}
                            {{ form.company_email(class="form-control") }}
                            {% if form.company_email.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.company_email.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-12 mb-3">
                            {{ form.company_website.label(class="form-label") }}
                            {{ form.company_website(class="form-control") }}
                            {% if form.company_website.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.company_website.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات العملة -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-coins me-2"></i>
                        إعدادات العملة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.currency_name.label(class="form-label") }}
                            {{ form.currency_name(class="form-control") }}
                            {% if form.currency_name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.currency_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.currency_symbol.label(class="form-label") }}
                            {{ form.currency_symbol(class="form-control") }}
                            {% if form.currency_symbol.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.currency_symbol.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.system_language.label(class="form-label") }}
                            {{ form.system_language(class="form-select") }}
                            {% if form.system_language.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.system_language.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.timezone.label(class="form-label") }}
                            {{ form.timezone(class="form-select") }}
                            {% if form.timezone.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.timezone.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.date_format.label(class="form-label") }}
                            {{ form.date_format(class="form-select") }}
                            {% if form.date_format.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.date_format.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات الشريط المتحرك -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-scroll me-2"></i>
                        إعدادات الشريط المتحرك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.enable_ticker.label(class="form-label") }}
                            {{ form.enable_ticker(class="form-select") }}
                            {% if form.enable_ticker.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.enable_ticker.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">إظهار شريط متحرك في أعلى الصفحة</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.ticker_speed.label(class="form-label") }}
                            {{ form.ticker_speed(class="form-select") }}
                            {% if form.ticker_speed.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.ticker_speed.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.ticker_content.label(class="form-label") }}
                            {{ form.ticker_content(class="form-select", id="ticker_content") }}
                            {% if form.ticker_content.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.ticker_content.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3" id="custom_text_field" style="display: none;">
                            {{ form.ticker_custom_text.label(class="form-label") }}
                            {{ form.ticker_custom_text(class="form-control") }}
                            {% if form.ticker_custom_text.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.ticker_custom_text.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معاينة الشريط المتحرك -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-eye me-2"></i>معاينة الشريط المتحرك:</h6>
                        <div class="ticker-preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px; border-radius: 5px; overflow: hidden; white-space: nowrap;">
                            <div style="display: inline-block; animation: scroll-rtl 10s linear infinite;">
                                <span id="preview-text">أحمد محمد • فاطمة علي • محمد حسن • عائشة أحمد • يوسف محمود</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>

                        <div>
                            <button type="button" class="btn btn-outline-warning me-2" onclick="confirmReset('system')">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>

                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- معلومات مساعدة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مساعدة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <h6>معلومات الشركة:</h6>
                    <p>تظهر في التقارير والإيصالات</p>

                    <h6>العملة:</h6>
                    <p>تستخدم في جميع المبالغ والحسابات</p>

                    <h6>المنطقة الزمنية:</h6>
                    <p>تؤثر على عرض التواريخ والأوقات</p>

                    <h6>تنسيق التاريخ:</h6>
                    <p>كيفية عرض التواريخ في النظام</p>

                    <h6>الشريط المتحرك:</h6>
                    <p>شريط متحرك في أعلى الصفحة يعرض معلومات مفيدة</p>

                    <h6>محتوى الشريط:</h6>
                    <ul class="small">
                        <li><strong>أسماء العمال:</strong> يعرض أسماء جميع العمال</li>
                        <li><strong>معلومات الشركة:</strong> يعرض اسم وبيانات الشركة</li>
                        <li><strong>نص مخصص:</strong> يعرض نص تختاره بنفسك</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيه
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-0">
                    تغيير هذه الإعدادات سيؤثر على جميع أجزاء النظام.
                    تأكد من صحة البيانات قبل الحفظ.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين إعدادات النظام للقيم الافتراضية؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم فقدان جميع الإعدادات المخصصة
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('reset_settings') }}" style="display: inline;">
                    <input type="hidden" name="category" value="system">
                    <button type="submit" class="btn btn-warning">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReset(category) {
    new bootstrap.Modal(document.getElementById('resetModal')).show();
}

// إظهار/إخفاء حقل النص المخصص
document.addEventListener('DOMContentLoaded', function() {
    const tickerContent = document.getElementById('ticker_content');
    const customTextField = document.getElementById('custom_text_field');

    function toggleCustomField() {
        if (tickerContent.value === 'custom') {
            customTextField.style.display = 'block';
        } else {
            customTextField.style.display = 'none';
        }
    }

    // تحقق من القيمة الحالية
    toggleCustomField();

    // استمع للتغييرات
    tickerContent.addEventListener('change', toggleCustomField);

    // تحديث معاينة الشريط المتحرك
    function updatePreview() {
        const previewText = document.getElementById('preview-text');
        const contentType = tickerContent.value;

        if (contentType === 'workers') {
            previewText.textContent = 'أحمد محمد • فاطمة علي • محمد حسن • عائشة أحمد • يوسف محمود';
        } else if (contentType === 'company_info') {
            previewText.textContent = 'مؤسسة الخدمات المنزلية - دولة قطر • هاتف: +974 1234 5678 • بريد: <EMAIL>';
        } else if (contentType === 'custom') {
            const customText = document.querySelector('input[name="ticker_custom_text"]').value;
            previewText.textContent = customText || 'النص المخصص';
        }
    }

    // تحديث المعاينة عند التغيير
    tickerContent.addEventListener('change', updatePreview);
    document.querySelector('input[name="ticker_custom_text"]').addEventListener('input', updatePreview);

    // تحديث المعاينة الأولي
    updatePreview();
});
</script>
{% endblock %}
