#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام صرف رواتب العمالة المنزلية - دولة قطر
تطبيق مستقل يعمل بدون إنترنت
"""

import os
import sys
import webbrowser
import threading
import time
from pathlib import Path

# إضافة المسار الحالي لـ Python path
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمد (exe)
    application_path = os.path.dirname(sys.executable)
    bundle_dir = sys._MEIPASS
else:
    # إذا كان يعمل من Python مباشرة
    application_path = os.path.dirname(os.path.abspath(__file__))
    bundle_dir = application_path

sys.path.insert(0, bundle_dir)

# استيراد التطبيق
from app import app, db

def create_database():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)  # انتظار حتى يبدأ الخادم
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح على: http://localhost:5000")
    except Exception as e:
        print(f"❌ خطأ في فتح المتصفح: {e}")
        print("🔗 يرجى فتح المتصفح يدوياً والذهاب إلى: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر")
    print("=" * 60)
    print("📱 تطبيق مستقل - يعمل بدون إنترنت")
    print("=" * 60)
    
    # إنشاء قاعدة البيانات
    create_database()
    
    # فتح المتصفح في thread منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("🚀 بدء تشغيل الخادم...")
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("📱 للوصول من أجهزة أخرى: http://[IP-ADDRESS]:5000")
    print("=" * 60)
    print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,  # إيقاف وضع التطوير للتطبيق المستقل
            use_reloader=False  # إيقاف إعادة التحميل التلقائي
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
