#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة تسجيل الدخول في النسخة المحمولة
"""

import os
import shutil
import sqlite3
from datetime import datetime
from werkzeug.security import generate_password_hash

def fix_portable_version():
    """إصلاح النسخة المحمولة"""
    
    print("🔧 إصلاح مشكلة تسجيل الدخول في النسخة المحمولة")
    print("=" * 60)
    
    # 1. التحقق من وجود الملف التنفيذي الأصلي
    original_exe = "dist/PayrollSystem.exe"
    portable_dir = "PayrollSystem_Portable"
    portable_exe = f"{portable_dir}/PayrollSystem.exe"
    
    if not os.path.exists(original_exe):
        print("❌ الملف التنفيذي الأصلي غير موجود")
        print("🔄 سيتم إنشاؤه الآن...")
        
        # إنشاء الملف التنفيذي
        os.system("python -m PyInstaller --onefile --name PayrollSystem --icon=static/app_icon.ico --add-data templates;templates --add-data static;static --add-data translations.py;. --add-data models.py;. --add-data forms.py;. --add-data config.py;. --add-data app.py;. run_standalone.py")
        
        if not os.path.exists(original_exe):
            print("❌ فشل في إنشاء الملف التنفيذي")
            return False
    
    print("✅ الملف التنفيذي الأصلي موجود")
    
    # 2. إنشاء مجلد النسخة المحمولة
    if os.path.exists(portable_dir):
        print("🗑️ حذف النسخة المحمولة القديمة...")
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir, exist_ok=True)
    print("📁 تم إنشاء مجلد النسخة المحمولة")
    
    # 3. نسخ الملف التنفيذي
    print("📦 نسخ الملف التنفيذي...")
    shutil.copy2(original_exe, portable_exe)
    
    # 4. إنشاء قاعدة بيانات للنسخة المحمولة
    portable_db = f"{portable_dir}/payroll.db"
    print("🗄️ إنشاء قاعدة بيانات للنسخة المحمولة...")
    
    create_portable_database(portable_db)
    
    # 5. نسخ الملفات المساعدة
    files_to_copy = [
        ("static/app_icon.ico", f"{portable_dir}/app_icon.ico"),
        ("static/portable_icon.ico", f"{portable_dir}/portable_icon.ico"),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ تم نسخ: {src}")
    
    # 6. إنشاء ملف التشغيل
    create_run_batch(portable_dir)
    
    # 7. إنشاء دليل الاستخدام
    create_portable_guide(portable_dir)
    
    print("\n" + "="*60)
    print("✅ تم إصلاح النسخة المحمولة بنجاح!")
    print("📁 المجلد: PayrollSystem_Portable")
    print("🚀 للتشغيل: انقر على PayrollSystem.exe")
    print("🔑 بيانات الدخول: admin / admin123")
    print("="*60)
    
    return True

def create_portable_database(db_path):
    """إنشاء قاعدة بيانات للنسخة المحمولة"""
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول العمال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS worker (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                nationality VARCHAR(50) NOT NULL,
                job_type VARCHAR(50) NOT NULL,
                passport_number VARCHAR(50),
                visa_number VARCHAR(50),
                phone VARCHAR(20),
                salary DECIMAL(10,2) NOT NULL,
                hire_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول الرواتب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                allowances DECIMAL(10,2) DEFAULT 0,
                deductions DECIMAL(10,2) DEFAULT 0,
                total_salary DECIMAL(10,2) NOT NULL,
                payment_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES worker (id)
            )
        ''')
        
        # إنشاء المستخدم الافتراضي
        password_hash = generate_password_hash('admin123')
        current_time = datetime.now()
        
        cursor.execute('''
            INSERT OR REPLACE INTO user (username, password_hash, full_name, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('admin', password_hash, 'مدير النظام', 'admin', True, current_time))
        
        # إنشاء مستخدم تجريبي
        test_password_hash = generate_password_hash('test123')
        cursor.execute('''
            INSERT OR REPLACE INTO user (username, password_hash, full_name, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('test', test_password_hash, 'مستخدم تجريبي', 'user', True, current_time))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات مع المستخدمين الافتراضيين")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def create_run_batch(portable_dir):
    """إنشاء ملف التشغيل"""
    
    batch_content = '''@echo off
chcp 65001 >nul
title نظام صرف رواتب العمالة المنزلية - نسخة محمولة

echo ============================================================
echo 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 📱 نسخة محمولة - تعمل من أي مكان
echo ============================================================

echo 🚀 بدء تشغيل النظام المحمول...
echo ⏳ يرجى الانتظار 10-30 ثانية...
echo 🌐 سيفتح المتصفح على: http://localhost:5000

start PayrollSystem.exe

timeout /t 15 /nobreak >nul

echo ✅ تم تشغيل النظام!
echo 🔗 إذا لم يفتح المتصفح: http://localhost:5000
echo 🔑 بيانات الدخول: admin / admin123
echo ============================================================
echo 💾 البيانات محفوظة في نفس المجلد
echo 🔄 يمكن نقل المجلد لأي مكان آخر
echo ============================================================

start http://localhost:5000

echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''
    
    batch_path = f"{portable_dir}/تشغيل_النظام.bat"
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف التشغيل")

def create_portable_guide(portable_dir):
    """إنشاء دليل الاستخدام"""
    
    guide_content = '''# 📱 نظام صرف رواتب العمالة المنزلية - النسخة المحمولة

## 🚀 طريقة التشغيل:

### ⚡ خطوة واحدة فقط:
1. انقر نقراً مزدوجاً على `PayrollSystem.exe`
2. انتظر 15 ثانية حتى يفتح المتصفح
3. سجل الدخول: `admin` / `admin123`

### 🔄 أو استخدم ملف التشغيل:
- انقر على `تشغيل_النظام.bat`

## 🔑 بيانات الدخول:
- الرابط: http://localhost:5000
- اسم المستخدم: admin
- كلمة المرور: admin123

## 💡 نصائح:
- تأكد من عدم حجب برامج الحماية للملف
- انتظر حتى يكتمل تحميل النظام
- استخدم متصفح حديث للحصول على أفضل تجربة

## 🆘 في حالة المشاكل:
1. جرب تشغيل `تشغيل_النظام.bat`
2. أضف المجلد لاستثناءات برنامج الحماية
3. تأكد من عدم استخدام المنفذ 5000 من برنامج آخر

مبروك! النسخة المحمولة جاهزة للاستخدام!
'''
    
    guide_path = f"{portable_dir}/دليل_الاستخدام_السريع.md"
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ تم إنشاء دليل الاستخدام")

def test_portable_version():
    """اختبار النسخة المحمولة"""
    
    print("\n🧪 اختبار النسخة المحمولة...")
    
    portable_exe = "PayrollSystem_Portable/PayrollSystem.exe"
    portable_db = "PayrollSystem_Portable/payroll.db"
    
    # فحص الملفات
    if os.path.exists(portable_exe):
        print("✅ الملف التنفيذي موجود")
    else:
        print("❌ الملف التنفيذي غير موجود")
        return False
    
    if os.path.exists(portable_db):
        print("✅ قاعدة البيانات موجودة")
    else:
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    # فحص قاعدة البيانات
    try:
        conn = sqlite3.connect(portable_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        print(f"✅ عدد المستخدمين: {user_count}")
        
        cursor.execute("SELECT username FROM user WHERE username = 'admin'")
        admin_exists = cursor.fetchone()
        if admin_exists:
            print("✅ المستخدم admin موجود")
        else:
            print("❌ المستخدم admin غير موجود")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False
    
    print("✅ النسخة المحمولة جاهزة للاستخدام!")
    return True

if __name__ == "__main__":
    if fix_portable_version():
        test_portable_version()
        
        print("\n💡 للاستخدام:")
        print("1. اذهب إلى مجلد PayrollSystem_Portable")
        print("2. انقر نقراً مزدوجاً على PayrollSystem.exe")
        print("3. انتظر 15 ثانية")
        print("4. سجل الدخول: admin / admin123")
    else:
        print("❌ فشل في إصلاح النسخة المحمولة")
