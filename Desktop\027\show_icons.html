<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 أيقونات نظام صرف رواتب العمالة المنزلية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #2980b9, #3498db);
            border-radius: 15px;
            color: white;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .icon-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }
        
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
        
        .icon-display {
            width: 128px;
            height: 128px;
            margin: 0 auto 20px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .app-icon { background-image: url('static/app_icon.png'); }
        .installer-icon { background-image: url('static/installer_icon.png'); }
        .portable-icon { background-image: url('static/portable_icon.png'); }
        .usb-icon { background-image: url('static/usb_icon.png'); }
        .service-icon { background-image: url('static/service_icon.png'); }
        
        .icon-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .icon-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .icon-usage {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 10px;
            font-size: 0.9em;
            color: #34495e;
        }
        
        .features {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .features h2 {
            margin-top: 0;
            font-size: 2em;
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-item h3 {
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .stats {
            background: #34495e;
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 1.1em;
            margin-top: 5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: #2c3e50;
            color: white;
            border-radius: 15px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .icons-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 أيقونات نظام صرف رواتب العمالة المنزلية</h1>
            <p>أيقونات احترافية مخصصة لجميع نسخ النظام</p>
        </div>
        
        <div class="icons-grid">
            <div class="icon-card">
                <div class="icon-display app-icon"></div>
                <div class="icon-title">🏠 أيقونة التطبيق الرئيسية</div>
                <div class="icon-description">
                    أيقونة النظام الرئيسية بتصميم احترافي يتضمن رمز الراتب والعملة القطرية
                </div>
                <div class="icon-usage">
                    <strong>الاستخدام:</strong><br>
                    • الملف التنفيذي الرئيسي<br>
                    • اختصارات سطح المكتب<br>
                    • شريط المهام<br>
                    • نوافذ النظام
                </div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display installer-icon"></div>
                <div class="icon-title">📦 أيقونة المثبت</div>
                <div class="icon-description">
                    أيقونة خضراء تدل على التثبيت والإعداد مع رمز صندوق التثبيت
                </div>
                <div class="icon-usage">
                    <strong>الاستخدام:</strong><br>
                    • ملفات التثبيت (.bat)<br>
                    • اختصارات المثبت<br>
                    • أدلة التثبيت<br>
                    • معالجات الإعداد
                </div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display portable-icon"></div>
                <div class="icon-title">📱 أيقونة النسخة المحمولة</div>
                <div class="icon-description">
                    أيقونة برتقالية تمثل الحمولة والنقل مع رمز حقيبة محمولة
                </div>
                <div class="icon-usage">
                    <strong>الاستخدام:</strong><br>
                    • النسخة المحمولة<br>
                    • الملف المضغوط<br>
                    • اختصارات النسخة المحمولة<br>
                    • ملفات النقل
                </div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display usb-icon"></div>
                <div class="icon-title">💾 أيقونة USB/فلاشة</div>
                <div class="icon-description">
                    أيقونة بنفسجية تمثل فلاشة USB مع LED للحالة النشطة
                </div>
                <div class="icon-usage">
                    <strong>الاستخدام:</strong><br>
                    • نسخة USB<br>
                    • ملف autorun.inf<br>
                    • اختصارات الفلاشة<br>
                    • التشغيل التلقائي
                </div>
            </div>
            
            <div class="icon-card">
                <div class="icon-display service-icon"></div>
                <div class="icon-title">🌐 أيقونة الخدمة/الشبكة</div>
                <div class="icon-description">
                    أيقونة زرقاء داكنة تمثل الخوادم والشبكة مع LED للحالة
                </div>
                <div class="icon-usage">
                    <strong>الاستخدام:</strong><br>
                    • خدمة الويب<br>
                    • اختصارات الشبكة<br>
                    • ملفات الخادم<br>
                    • إدارة الخدمات
                </div>
            </div>
        </div>
        
        <div class="features">
            <h2>🌟 مميزات الأيقونات</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <h3>🎨 تصميم احترافي</h3>
                    <p>ألوان متناسقة وتدرجات جذابة مع رموز واضحة ومفهومة</p>
                </div>
                <div class="feature-item">
                    <h3>📏 متعددة الأحجام</h3>
                    <p>من 16x16 إلى 256x256 بكسل لجميع الاستخدامات</p>
                </div>
                <div class="feature-item">
                    <h3>🔍 وضوح عالي</h3>
                    <p>واضحة في جميع الأحجام مع شفافية كاملة</p>
                </div>
                <div class="feature-item">
                    <h3>🔄 متوافقة</h3>
                    <p>تعمل مع جميع إصدارات Windows</p>
                </div>
            </div>
        </div>
        
        <div class="stats">
            <h2>📊 إحصائيات الأيقونات</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">أيقونات رئيسية</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">ملفات إجمالي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">30</div>
                    <div class="stat-label">حجم مختلف</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">65</div>
                    <div class="stat-label">KB حجم إجمالي</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <h3>🇶🇦 نظام صرف رواتب العمالة المنزلية - دولة قطر</h3>
            <p>الإصدار 13.1 - Professional Icons Edition</p>
            <p>تاريخ الإنشاء: 29 مايو 2025</p>
            <p><strong>✅ جميع الأيقونات مُضافة ومُطبقة بنجاح!</strong></p>
        </div>
    </div>
</body>
</html>
