@echo off
chcp 65001 >nul
title تثبيت نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🏠 تثبيت نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================

echo 📁 إنشاء مجلد التطبيق...
set "INSTALL_DIR=%USERPROFILE%\Desktop\نظام الرواتب"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📦 نسخ ملفات التطبيق...
copy "PayrollSystem.exe" "%INSTALL_DIR%\"
copy "run_exe.bat" "%INSTALL_DIR%\"
copy "دليل_الاستخدام.md" "%INSTALL_DIR%\"

echo 🔗 إنشاء اختصار على سطح المكتب...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام الرواتب.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\PayrollSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\PayrollSystem.exe'; $Shortcut.Save()"

echo 📋 إنشاء ملف إلغاء التثبيت...
echo @echo off > "%INSTALL_DIR%\uninstall.bat"
echo echo حذف نظام الرواتب... >> "%INSTALL_DIR%\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\uninstall.bat"
echo del "%USERPROFILE%\Desktop\نظام الرواتب.lnk" >> "%INSTALL_DIR%\uninstall.bat"
echo echo تم حذف النظام بنجاح! >> "%INSTALL_DIR%\uninstall.bat"
echo pause >> "%INSTALL_DIR%\uninstall.bat"

echo ✅ تم التثبيت بنجاح!
echo 🚀 يمكنك الآن تشغيل النظام من:
echo    - الاختصار على سطح المكتب
echo    - مجلد: %INSTALL_DIR%
echo ============================================================
echo 🌐 الرابط: http://localhost:5000
echo 🔑 المستخدم: admin / كلمة المرور: admin123
echo ============================================================
pause
