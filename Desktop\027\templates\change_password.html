{% extends "base.html" %}

{% block title %}تغيير كلمة المرور - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-key me-2"></i>تغيير كلمة المرور</h2>
            <p class="text-muted mb-0">تحديث كلمة المرور الخاصة بك</p>
        </div>
        <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للملف الشخصي
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-lock me-2"></i>
                    تغيير كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- كلمة المرور الحالية -->
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control") }}
                        {% if form.current_password.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.current_password.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- كلمة المرور الجديدة -->
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control") }}
                        {% if form.new_password.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.new_password.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="text-muted">يجب أن تكون كلمة المرور 4 أحرف على الأقل</small>
                    </div>
                    
                    <!-- تأكيد كلمة المرور الجديدة -->
                    <div class="mb-4">
                        {{ form.confirm_password.label(class="form-label") }}
                        {{ form.confirm_password(class="form-control") }}
                        {% if form.confirm_password.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.confirm_password.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- نصائح الأمان -->
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    نصائح لكلمة مرور آمنة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم على الأقل 8 أحرف
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        امزج بين الأحرف الكبيرة والصغيرة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أضف أرقام ورموز خاصة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تجنب المعلومات الشخصية الواضحة
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        غير كلمة المرور بانتظام
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
