@echo off
chcp 65001 >nul
title تثبيت خدمة نظام الرواتب

echo ============================================================
echo 📦 تثبيت خدمة نظام صرف رواتب العمالة المنزلية
echo ============================================================

echo 📁 إنشاء مجلد الخدمة...
set "SERVICE_DIR=%ProgramFiles%\PayrollService"
if not exist "%SERVICE_DIR%" mkdir "%SERVICE_DIR%"

echo 📦 نسخ ملفات الخدمة...
copy "../dist/PayrollSystem.exe" "%SERVICE_DIR%\"
copy "start_service.bat" "%SERVICE_DIR%\"
copy "stop_service.bat" "%SERVICE_DIR%\"
copy "../static/service_icon.ico" "%SERVICE_DIR%\"
copy "../static/app_icon.ico" "%SERVICE_DIR%\"

echo 🔗 إنشاء اختصارات مع أيقونات مخصصة...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\بدء خدمة الرواتب.lnk'); $Shortcut.TargetPath = '%SERVICE_DIR%\start_service.bat'; $Shortcut.WorkingDirectory = '%SERVICE_DIR%'; $Shortcut.IconLocation = '%SERVICE_DIR%\service_icon.ico'; $Shortcut.Save()"

powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\إيقاف خدمة الرواتب.lnk'); $Shortcut.TargetPath = '%SERVICE_DIR%\stop_service.bat'; $Shortcut.WorkingDirectory = '%SERVICE_DIR%'; $Shortcut.IconLocation = '%SERVICE_DIR%\service_icon.ico'; $Shortcut.Save()"

echo ✅ تم تثبيت الخدمة بنجاح!
echo 🚀 يمكنك الآن تشغيل الخدمة من سطح المكتب
echo ============================================================
pause
