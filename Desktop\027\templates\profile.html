{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-user me-2"></i>الملف الشخصي</h2>
            <p class="text-muted mb-0">عرض وإدارة بياناتك الشخصية</p>
        </div>
        <a href="{{ url_for('change_password') }}" class="btn btn-outline-primary">
            <i class="fas fa-key me-2"></i>
            تغيير كلمة المرور
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-id-card me-2"></i>
                    البيانات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">اسم المستخدم</label>
                        <p class="h5">{{ user.username }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم الكامل</label>
                        <p class="h5">{{ user.full_name }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الدور</label>
                        <p>
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger fs-6">مدير النظام</span>
                            {% elif user.role == 'manager' %}
                                <span class="badge bg-warning fs-6">مدير</span>
                            {% elif user.role == 'employee' %}
                                <span class="badge bg-primary fs-6">موظف</span>
                            {% else %}
                                <span class="badge bg-secondary fs-6">مستعرض فقط</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة الحساب</label>
                        <p>
                            {% if user.is_active %}
                                <span class="badge bg-success fs-6">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary fs-6">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ إنشاء الحساب</label>
                        <p>{{ user.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">آخر تسجيل دخول</label>
                        <p class="text-success">متصل الآن</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('change_password') }}" class="btn btn-outline-primary">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </a>
                    
                    {% if user.role == 'admin' %}
                    <a href="{{ url_for('users') }}" class="btn btn-outline-info">
                        <i class="fas fa-users-cog me-2"></i>
                        إدارة المستخدمين
                    </a>
                    {% endif %}
                    
                    <hr>
                    
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    الصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% if user.role in ['admin', 'manager', 'employee'] %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>إدارة العمال</span>
                        <i class="fas fa-check text-success"></i>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>إدارة الرواتب</span>
                        <i class="fas fa-check text-success"></i>
                    </div>
                    {% endif %}
                    
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>عرض التقارير</span>
                        <i class="fas fa-check text-success"></i>
                    </div>
                    
                    {% if user.role == 'admin' %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>إدارة المستخدمين</span>
                        <i class="fas fa-check text-success"></i>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>إعدادات النظام</span>
                        <i class="fas fa-check text-success"></i>
                    </div>
                    {% endif %}
                    
                    {% if user.role == 'viewer' %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>تعديل البيانات</span>
                        <i class="fas fa-times text-danger"></i>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h6 class="text-muted">اسم النظام</h6>
                        <p class="mb-0">نظام صرف رواتب العمالة المنزلية</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">الدولة</h6>
                        <p class="mb-0">دولة قطر 🇶🇦</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">العملة</h6>
                        <p class="mb-0">الريال القطري (ر.ق)</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">الإصدار</h6>
                        <p class="mb-0">2.0 - Qatar Edition</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
