#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء ملفات تشغيل النظام كخدمة ويب
"""

import os
from pathlib import Path

def create_service_files():
    """إنشاء ملفات الخدمة"""

    # إنشاء مجلد الخدمة
    service_dir = Path("service")
    service_dir.mkdir(exist_ok=True)

    # إنشاء ملف تشغيل الخدمة
    create_service_launcher()

    # إنشاء ملف إيقاف الخدمة
    create_service_stopper()

    # إنشاء ملف تثبيت الخدمة
    create_service_installer()

    # إنشاء دليل الخدمة
    create_service_guide()

    print("✅ تم إنشاء ملفات الخدمة بنجاح!")

def create_service_launcher():
    """إنشاء ملف تشغيل الخدمة"""
    launcher_script = '''@echo off
chcp 65001 >nul
title خدمة نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🌐 خدمة نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 🖥️ تشغيل كخدمة ويب على الشبكة المحلية
echo ============================================================

echo 🔍 البحث عن الملف التنفيذي...
if exist "../dist/PayrollSystem.exe" (
    echo ✅ تم العثور على الملف التنفيذي

    echo 🌐 الحصول على عنوان IP...
    for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
        set "IP=%%a"
        goto :found
    )
    :found
    set IP=%IP: =%

    echo ============================================================
    echo 🚀 بدء تشغيل الخدمة...
    echo 🌐 الخدمة متاحة على:
    echo    - محلي: http://localhost:5000
    echo    - الشبكة: http://%IP%:5000
    echo 🔑 بيانات الدخول: admin / admin123
    echo ============================================================
    echo ⚠️ لا تغلق هذه النافذة - الخدمة تعمل
    echo 🛑 لإيقاف الخدمة: اضغط Ctrl+C أو أغلق النافذة
    echo ============================================================

    cd ../dist
    PayrollSystem.exe

) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 🏗️ يرجى بناء الملف التنفيذي أولاً
    pause
)
'''

    with open("service/start_service.bat", "w", encoding="utf-8") as f:
        f.write(launcher_script)

def create_service_stopper():
    """إنشاء ملف إيقاف الخدمة"""
    stopper_script = '''@echo off
chcp 65001 >nul
title إيقاف خدمة نظام الرواتب

echo ============================================================
echo 🛑 إيقاف خدمة نظام صرف رواتب العمالة المنزلية
echo ============================================================

echo 🔍 البحث عن العمليات النشطة...
tasklist | findstr "PayrollSystem.exe" >nul
if %errorlevel%==0 (
    echo ✅ تم العثور على الخدمة النشطة
    echo 🛑 إيقاف الخدمة...
    taskkill /f /im "PayrollSystem.exe" >nul 2>&1
    echo ✅ تم إيقاف الخدمة بنجاح!
) else (
    echo ❌ الخدمة غير نشطة
)

echo ============================================================
pause
'''

    with open("service/stop_service.bat", "w", encoding="utf-8") as f:
        f.write(stopper_script)

def create_service_installer():
    """إنشاء ملف تثبيت الخدمة"""
    installer_script = '''@echo off
chcp 65001 >nul
title تثبيت خدمة نظام الرواتب

echo ============================================================
echo 📦 تثبيت خدمة نظام صرف رواتب العمالة المنزلية
echo ============================================================

echo 📁 إنشاء مجلد الخدمة...
set "SERVICE_DIR=%ProgramFiles%\\PayrollService"
if not exist "%SERVICE_DIR%" mkdir "%SERVICE_DIR%"

echo 📦 نسخ ملفات الخدمة...
copy "../dist/PayrollSystem.exe" "%SERVICE_DIR%\\"
copy "start_service.bat" "%SERVICE_DIR%\\"
copy "stop_service.bat" "%SERVICE_DIR%\\"
copy "../static/service_icon.ico" "%SERVICE_DIR%\\"
copy "../static/app_icon.ico" "%SERVICE_DIR%\\"

echo 🔗 إنشاء اختصارات مع أيقونات مخصصة...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\\Desktop\\بدء خدمة الرواتب.lnk'); $Shortcut.TargetPath = '%SERVICE_DIR%\\start_service.bat'; $Shortcut.WorkingDirectory = '%SERVICE_DIR%'; $Shortcut.IconLocation = '%SERVICE_DIR%\\service_icon.ico'; $Shortcut.Save()"

powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\\Desktop\\إيقاف خدمة الرواتب.lnk'); $Shortcut.TargetPath = '%SERVICE_DIR%\\stop_service.bat'; $Shortcut.WorkingDirectory = '%SERVICE_DIR%'; $Shortcut.IconLocation = '%SERVICE_DIR%\\service_icon.ico'; $Shortcut.Save()"

echo ✅ تم تثبيت الخدمة بنجاح!
echo 🚀 يمكنك الآن تشغيل الخدمة من سطح المكتب
echo ============================================================
pause
'''

    with open("service/install_service.bat", "w", encoding="utf-8") as f:
        f.write(installer_script)

def create_service_guide():
    """إنشاء دليل الخدمة"""
    guide_content = '''# 🌐 دليل تشغيل النظام كخدمة ويب

## 🎯 مميزات الخدمة:
- ✅ تعمل في الخلفية
- ✅ متاحة لجميع أجهزة الشبكة
- ✅ تشغيل تلقائي مع النظام
- ✅ إدارة مركزية للبيانات

## 🚀 طرق التثبيت:

### 📦 الطريقة الأولى: التثبيت التلقائي
1. شغل `install_service.bat` كمدير
2. ستظهر اختصارات على سطح المكتب
3. استخدم "بدء خدمة الرواتب" للتشغيل

### 🔧 الطريقة الثانية: التشغيل اليدوي
1. شغل `start_service.bat`
2. اتبع التعليمات على الشاشة
3. استخدم `stop_service.bat` للإيقاف

## 🌐 الوصول للخدمة:

### 🖥️ من نفس الجهاز:
```
http://localhost:5000
```

### 📱 من أجهزة أخرى في الشبكة:
```
http://[عنوان-IP-للخادم]:5000
مثال: http://*************:5000
```

### 🔍 معرفة عنوان IP:
```cmd
ipconfig
# ابحث عن IPv4 Address
```

## ⚙️ إعدادات الشبكة:

### 🔥 إعدادات Firewall:
1. افتح Windows Defender Firewall
2. اختر "Allow an app through firewall"
3. أضف PayrollSystem.exe
4. تأكد من تحديد Private و Public

### 🌐 إعدادات الراوتر (اختياري):
- للوصول من خارج الشبكة المحلية
- أضف Port Forwarding للمنفذ 5000
- وجه إلى عنوان IP للخادم

## 🔒 الأمان:
- غير كلمة مرور admin الافتراضية
- استخدم شبكة آمنة
- فعل Firewall
- راقب الوصول للنظام

## 🛠️ استكشاف الأخطاء:

### ❌ لا يمكن الوصول من أجهزة أخرى:
- تحقق من إعدادات Firewall
- تأكد من أن الأجهزة في نفس الشبكة
- جرب إيقاف Firewall مؤقتاً للاختبار

### 🐌 الخدمة بطيئة:
- تحقق من سرعة الشبكة
- أغلق البرامج غير الضرورية
- استخدم جهاز أقوى كخادم

## 📊 مراقبة الخدمة:
- راقب استهلاك الذاكرة والمعالج
- تحقق من سجلات النظام
- اعمل نسخ احتياطية دورية

تاريخ الإنشاء: 29 مايو 2025
الإصدار: 13.0 - Service Edition
'''

    with open("service/دليل_الخدمة.md", "w", encoding="utf-8") as f:
        f.write(guide_content)

if __name__ == "__main__":
    create_service_files()
