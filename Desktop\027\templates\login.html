{% extends "base.html" %}

{% block title %}{{ _('login') }} - {{ _('home_payroll_system') }}{% endblock %}

{% block login_content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Login Form -->
        <div class="col-md-6 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-money-check-alt fa-3x text-primary mb-3"></i>
                        <h3 class="card-title">{{ _('login') }}</h3>
                        <p class="text-muted">{{ _('home_payroll_system') }}</p>
                    </div>

                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control form-control-lg", placeholder=_('username')) }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.username.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control form-control-lg", placeholder=_('password')) }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            للحصول على بيانات الدخول، تواصل مع مدير النظام
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right side - Welcome Message -->
        <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-5">
                <i class="fas fa-home fa-5x mb-4"></i>
                <h2 class="mb-4">مرحباً بك في نظام إدارة رواتب العمالة المنزلية</h2>
                <p class="lead mb-4">
                    نظام شامل لإدارة بيانات العمالة المنزلية وصرف رواتبهم الشهرية
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>إدارة العمال</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <p>صرف الرواتب</p>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-chart-bar fa-2x mb-2"></i>
                        <p>التقارير</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
