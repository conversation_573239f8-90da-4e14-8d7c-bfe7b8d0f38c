{% extends "base.html" %}

{% block title %}إدارة العمال - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-users me-2"></i>إدارة العمال</h2>
            <p class="text-muted mb-0">إضافة وتعديل وإدارة بيانات العمالة المنزلية</p>
        </div>
        <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة عامل جديد
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control"
                       placeholder="البحث بالاسم، الجنسية، نوع العمل، أو رقم الهوية/الإقامة/الفيزا"
                       value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                    <option value="terminated" {% if status_filter == 'terminated' %}selected{% endif %}>منتهي الخدمة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('workers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Workers Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة العمال
            {% if workers.total %}
                <span class="badge bg-primary">{{ workers.total }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if workers.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الاسم</th>
                            <th>الجنسية</th>
                            <th>نوع العمل</th>
                            <th>رقم الهوية/الإقامة/الفيزا</th>
                            <th>رقم الجوال</th>
                            <th>الراتب الأساسي</th>
                            <th>الحالة</th>
                            <th>تاريخ التوظيف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for worker in workers.items %}
                        <tr>
                            <td>
                                <strong>{{ worker.name }}</strong>
                            </td>
                            <td>{{ worker.nationality }}</td>
                            <td>{{ worker.job_type }}</td>
                            <td>{{ worker.id_number }}</td>
                            <td>{{ worker.phone or '-' }}</td>
                            <td>
                                <span class="fw-bold text-success">
                                    {{ "{:,.0f}".format(worker.basic_salary) }} ر.ق
                                </span>
                            </td>
                            <td>
                                {% if worker.status == 'active' %}
                                    <span class="badge bg-success">نشط</span>
                                {% elif worker.status == 'inactive' %}
                                    <span class="badge bg-warning">غير نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">منتهي الخدمة</span>
                                {% endif %}
                            </td>
                            <td>{{ worker.hire_date.strftime('%Y/%m/%d') }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('edit_worker', id=worker.id) }}"
                                       class="btn btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="confirmDelete({{ worker.id }}, '{{ worker.name }}')"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if workers.pages > 1 %}
            <div class="card-footer bg-white">
                <nav aria-label="صفحات العمال">
                    <ul class="pagination justify-content-center mb-0">
                        {% if workers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('workers', page=workers.prev_num, search=search, status=status_filter) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for page_num in workers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != workers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('workers', page=page_num, search=search, status=status_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if workers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('workers', page=workers.next_num, search=search, status=status_filter) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عمال</h5>
                <p class="text-muted">لم يتم العثور على أي عمال مطابقين لمعايير البحث</p>
                <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة أول عامل
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="workerName"></strong>؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = '/workers/delete/' + workerId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
