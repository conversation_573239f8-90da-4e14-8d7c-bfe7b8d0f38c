{% extends "base.html" %}

{% block title %}التقارير - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-chart-bar me-2"></i>التقارير</h2>
            <p class="text-muted mb-0">تصدير وطباعة تقارير الرواتب</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- Report Filter -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    معايير التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('generate_report') }}" id="reportForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.month.label(class="form-label") }}
                        {{ form.month(class="form-select") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.year.label(class="form-label") }}
                        {{ form.year(class="form-control") }}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.payment_status.label(class="form-label") }}
                        {{ form.payment_status(class="form-select") }}
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="generateReport('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>
                            تصدير PDF
                        </button>
                        
                        <button type="button" class="btn btn-success" onclick="generateReport('excel')">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                        
                        <button type="button" class="btn btn-info" onclick="previewReport()">
                            <i class="fas fa-eye me-2"></i>
                            معاينة التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Quick Reports -->
        <div class="card mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    تقارير سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="quickReport('current_month')">
                        <i class="fas fa-calendar-alt me-2"></i>
                        رواتب الشهر الحالي
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning" onclick="quickReport('unpaid')">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        الرواتب غير المدفوعة
                    </button>
                    
                    <button type="button" class="btn btn-outline-success" onclick="quickReport('paid_this_month')">
                        <i class="fas fa-check-circle me-2"></i>
                        المدفوع هذا الشهر
                    </button>
                    
                    <button type="button" class="btn btn-outline-info" onclick="quickReport('workers_summary')">
                        <i class="fas fa-users me-2"></i>
                        ملخص العمال
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Report Preview -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة التقرير
                </h5>
            </div>
            <div class="card-body" id="reportPreview">
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">اختر معايير التقرير واضغط "معاينة التقرير"</h5>
                    <p class="text-muted">سيتم عرض التقرير هنا قبل التصدير</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1" id="totalWorkers">-</h3>
                    <p class="mb-0">إجمالي العمال</p>
                </div>
                <i class="fas fa-users fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1" id="totalSalaries">-</h3>
                    <p class="mb-0">إجمالي الرواتب</p>
                </div>
                <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1" id="paidAmount">-</h3>
                    <p class="mb-0">المبلغ المدفوع</p>
                </div>
                <i class="fas fa-check-circle fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-1" id="unpaidAmount">-</h3>
                    <p class="mb-0">المبلغ غير المدفوع</p>
                </div>
                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0">جاري إنشاء التقرير...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إنشاء التقرير
function generateReport(format) {
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);
    formData.append('format', format);
    
    // إظهار نافذة التحميل
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    fetch('/reports/generate', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('فشل في إنشاء التقرير');
    })
    .then(blob => {
        // تحميل الملف
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        const month = document.getElementById('month').value;
        const year = document.getElementById('year').value;
        const monthName = document.getElementById('month').options[document.getElementById('month').selectedIndex].text;
        
        if (format === 'pdf') {
            a.download = `تقرير_الرواتب_${monthName}_${year}.pdf`;
        } else {
            a.download = `تقرير_الرواتب_${monthName}_${year}.xlsx`;
        }
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        loadingModal.hide();
    })
    .catch(error => {
        console.error('Error:', error);
        loadingModal.hide();
        alert('حدث خطأ في إنشاء التقرير');
    });
}

// معاينة التقرير
function previewReport() {
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);
    
    fetch('/reports/preview', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        displayReportPreview(data);
        updateStatistics(data.statistics);
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('reportPreview').innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h5 class="text-danger">حدث خطأ في تحميل التقرير</h5>
            </div>
        `;
    });
}

// عرض معاينة التقرير
function displayReportPreview(data) {
    let html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>العامل</th>
                        <th>الشهر</th>
                        <th>الراتب الأساسي</th>
                        <th>البدلات</th>
                        <th>الخصومات</th>
                        <th>صافي الراتب</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    if (data.salaries && data.salaries.length > 0) {
        data.salaries.forEach(salary => {
            html += `
                <tr>
                    <td>${salary.worker_name}</td>
                    <td>${salary.month_name} ${salary.year}</td>
                    <td>${salary.basic_salary.toLocaleString('ar-SA')} ريال</td>
                    <td>${salary.allowances.toLocaleString('ar-SA')} ريال</td>
                    <td>${salary.deductions.toLocaleString('ar-SA')} ريال</td>
                    <td class="fw-bold">${salary.net_salary.toLocaleString('ar-SA')} ريال</td>
                    <td>
                        ${salary.payment_status === 'paid' ? 
                            '<span class="badge bg-success">مدفوع</span>' : 
                            '<span class="badge bg-warning">غير مدفوع</span>'
                        }
                    </td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <br>لا توجد بيانات للعرض
                </td>
            </tr>
        `;
    }
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    if (data.summary) {
        html += `
            <div class="row mt-4">
                <div class="col-md-3 text-center">
                    <h6 class="text-muted">إجمالي الرواتب</h6>
                    <h5 class="text-primary">${data.summary.total_amount.toLocaleString('ar-SA')} ريال</h5>
                </div>
                <div class="col-md-3 text-center">
                    <h6 class="text-muted">المدفوع</h6>
                    <h5 class="text-success">${data.summary.paid_amount.toLocaleString('ar-SA')} ريال</h5>
                </div>
                <div class="col-md-3 text-center">
                    <h6 class="text-muted">غير المدفوع</h6>
                    <h5 class="text-danger">${data.summary.unpaid_amount.toLocaleString('ar-SA')} ريال</h5>
                </div>
                <div class="col-md-3 text-center">
                    <h6 class="text-muted">عدد العمال</h6>
                    <h5 class="text-info">${data.summary.worker_count}</h5>
                </div>
            </div>
        `;
    }
    
    document.getElementById('reportPreview').innerHTML = html;
}

// تحديث الإحصائيات
function updateStatistics(stats) {
    if (stats) {
        document.getElementById('totalWorkers').textContent = stats.total_workers || '-';
        document.getElementById('totalSalaries').textContent = (stats.total_salaries || 0).toLocaleString('ar-SA');
        document.getElementById('paidAmount').textContent = (stats.paid_amount || 0).toLocaleString('ar-SA');
        document.getElementById('unpaidAmount').textContent = (stats.unpaid_amount || 0).toLocaleString('ar-SA');
    }
}

// التقارير السريعة
function quickReport(type) {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    switch(type) {
        case 'current_month':
            document.getElementById('month').value = currentMonth;
            document.getElementById('year').value = currentYear;
            document.getElementById('payment_status').value = 'all';
            break;
        case 'unpaid':
            document.getElementById('month').value = 0;
            document.getElementById('year').value = currentYear;
            document.getElementById('payment_status').value = 'unpaid';
            break;
        case 'paid_this_month':
            document.getElementById('month').value = currentMonth;
            document.getElementById('year').value = currentYear;
            document.getElementById('payment_status').value = 'paid';
            break;
        case 'workers_summary':
            // يمكن إضافة تقرير خاص بملخص العمال
            break;
    }
    
    previewReport();
}

// تحميل الإحصائيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/statistics')
        .then(response => response.json())
        .then(data => updateStatistics(data))
        .catch(error => console.error('Error loading statistics:', error));
});
</script>
{% endblock %}
