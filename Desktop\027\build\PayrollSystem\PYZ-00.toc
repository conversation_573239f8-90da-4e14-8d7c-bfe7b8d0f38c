('C:\\Users\\<USER>\\Desktop\\027\\build\\PayrollSystem\\PYZ-00.pyz',
 [('IPython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.doctb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\doctb.py',
   'PYMODULE'),
  ('IPython.core.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.payloadpage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\payloadpage.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.tbtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\tbtools.py',
   'PYMODULE'),
  ('IPython.core.tips',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\tips.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.pickleshare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\external\\pickleshare.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.guisupport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\lib\\guisupport.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize', 'C:\\Program Files\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Program Files\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Program Files\\Python313\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Program Files\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Program Files\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Program Files\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('app', 'C:\\Users\\<USER>\\Desktop\\027\\app.py', 'PYMODULE'),
  ('arabic_reshaper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\arabic_reshaper\\__init__.py',
   'PYMODULE'),
  ('arabic_reshaper.arabic_reshaper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\arabic_reshaper\\arabic_reshaper.py',
   'PYMODULE'),
  ('arabic_reshaper.letters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\arabic_reshaper\\letters.py',
   'PYMODULE'),
  ('arabic_reshaper.ligatures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\arabic_reshaper\\ligatures.py',
   'PYMODULE'),
  ('arabic_reshaper.reshaper_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\arabic_reshaper\\reshaper_config.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('asgiref',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('asgiref.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('babel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\__init__.py',
   'PYMODULE'),
  ('babel.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\core.py',
   'PYMODULE'),
  ('babel.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\dates.py',
   'PYMODULE'),
  ('babel.localedata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localedata.py',
   'PYMODULE'),
  ('babel.localtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localtime\\__init__.py',
   'PYMODULE'),
  ('babel.localtime._fallback',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localtime\\_fallback.py',
   'PYMODULE'),
  ('babel.localtime._helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localtime\\_helpers.py',
   'PYMODULE'),
  ('babel.localtime._unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localtime\\_unix.py',
   'PYMODULE'),
  ('babel.localtime._win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\localtime\\_win32.py',
   'PYMODULE'),
  ('babel.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\numbers.py',
   'PYMODULE'),
  ('babel.plural',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\plural.py',
   'PYMODULE'),
  ('babel.support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\babel\\support.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bidi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bidi\\__init__.py',
   'PYMODULE'),
  ('bidi.algorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bidi\\algorithm.py',
   'PYMODULE'),
  ('bidi.mirror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bidi\\mirror.py',
   'PYMODULE'),
  ('bidi.wrapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bidi\\wrapper.py',
   'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bs4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('cProfile', 'C:\\Program Files\\Python313\\Lib\\cProfile.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Program Files\\Python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('comm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comm\\__init__.py',
   'PYMODULE'),
  ('comm.base_comm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\comm\\base_comm.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'C:\\Users\\<USER>\\Desktop\\027\\config.py', 'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Program Files\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Program Files\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('debugpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\_vendored\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\_vendored\\_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\_vendored\\force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\adapter\\__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\__init__.py',
   'PYMODULE'),
  ('debugpy.common.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\common\\util.py',
   'PYMODULE'),
  ('debugpy.public_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\public_api.py',
   'PYMODULE'),
  ('debugpy.server',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\server\\__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\debugpy\\server\\api.py',
   'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\decorator.py',
   'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_asyncio_backend.py',
   'PYMODULE'),
  ('dns._ddr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_ddr.py',
   'PYMODULE'),
  ('dns._features',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_features.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_immutable_ctx.py',
   'PYMODULE'),
  ('dns._trio_backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\_trio_backend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncquery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\asyncquery.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\asyncresolver.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\dnssectypes.py',
   'PYMODULE'),
  ('dns.edns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.entropy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.enum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.exception',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('dns.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dns.grange',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\grange.py',
   'PYMODULE'),
  ('dns.immutable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\immutable.py',
   'PYMODULE'),
  ('dns.inet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.ipv4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.ipv6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.message',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.nameserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\nameserver.py',
   'PYMODULE'),
  ('dns.node',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.opcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('dns.quic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\quic\\__init__.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\quic\\_asyncio.py',
   'PYMODULE'),
  ('dns.quic._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\quic\\_common.py',
   'PYMODULE'),
  ('dns.quic._sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\quic\\_sync.py',
   'PYMODULE'),
  ('dns.quic._trio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\quic\\_trio.py',
   'PYMODULE'),
  ('dns.rcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.rdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rdataset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.renderer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.resolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns.reversename',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rrset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.serial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.set',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.transaction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\transaction.py',
   'PYMODULE'),
  ('dns.tsig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.ttl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.update',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.win32util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\win32util.py',
   'PYMODULE'),
  ('dns.wire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.xfr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\xfr.py',
   'PYMODULE'),
  ('dns.zone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\zone.py',
   'PYMODULE'),
  ('dns.zonefile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\zonefile.py',
   'PYMODULE'),
  ('dns.zonetypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dns\\zonetypes.py',
   'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email_validator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\__init__.py',
   'PYMODULE'),
  ('email_validator.deliverability',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\deliverability.py',
   'PYMODULE'),
  ('email_validator.exceptions_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\exceptions_types.py',
   'PYMODULE'),
  ('email_validator.rfc_constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\rfc_constants.py',
   'PYMODULE'),
  ('email_validator.syntax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\syntax.py',
   'PYMODULE'),
  ('email_validator.validate_email',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\email_validator\\validate_email.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('executing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing._pytest_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\_pytest_utils.py',
   'PYMODULE'),
  ('executing.executing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('filecmp', 'C:\\Program Files\\Python313\\Lib\\filecmp.py', 'PYMODULE'),
  ('fileinput', 'C:\\Program Files\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_babel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_babel\\__init__.py',
   'PYMODULE'),
  ('flask_babel.speaklater',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_babel\\speaklater.py',
   'PYMODULE'),
  ('flask_sqlalchemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('flask_sqlalchemy.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\cli.py',
   'PYMODULE'),
  ('flask_sqlalchemy.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\extension.py',
   'PYMODULE'),
  ('flask_sqlalchemy.model',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\model.py',
   'PYMODULE'),
  ('flask_sqlalchemy.pagination',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\pagination.py',
   'PYMODULE'),
  ('flask_sqlalchemy.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\query.py',
   'PYMODULE'),
  ('flask_sqlalchemy.record_queries',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\record_queries.py',
   'PYMODULE'),
  ('flask_sqlalchemy.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\session.py',
   'PYMODULE'),
  ('flask_sqlalchemy.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\table.py',
   'PYMODULE'),
  ('flask_sqlalchemy.track_modifications',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_sqlalchemy\\track_modifications.py',
   'PYMODULE'),
  ('flask_wtf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.csrf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\csrf.py',
   'PYMODULE'),
  ('flask_wtf.form',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\form.py',
   'PYMODULE'),
  ('flask_wtf.i18n',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\i18n.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\recaptcha\\__init__.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\recaptcha\\fields.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\recaptcha\\validators.py',
   'PYMODULE'),
  ('flask_wtf.recaptcha.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_wtf\\recaptcha\\widgets.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fontTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.agl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.misc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.pens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_V_A_R_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('forms', 'C:\\Users\\<USER>\\Desktop\\027\\forms.py', 'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Program Files\\Python313\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('ipykernel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\comm\\__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\comm\\comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\comm\\manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\displayhook.py',
   'PYMODULE'),
  ('ipykernel.embed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\embed.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\gui\\__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\gui\\gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\gui\\gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\pickleutil.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\ipykernel\\zmqshell.py',
   'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jedi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'C:\\Program Files\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jupyter_client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\asynchronous\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\asynchronous\\client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\blocking\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\blocking\\client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\provisioning\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\provisioning\\factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\provisioning\\local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\provisioning\\provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\ssh\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\ssh\\forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\ssh\\tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_client\\win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_core\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_core\\paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_core\\utils\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\jupyter_core\\version.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Program Files\\Python313\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program Files\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('models', 'C:\\Users\\<USER>\\Desktop\\027\\models.py', 'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nest_asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\nest_asyncio.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('ntsecuritycon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\ntsecuritycon.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python313\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('parso',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Program Files\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Program Files\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'C:\\Program Files\\Python313\\Lib\\profile.py', 'PYMODULE'),
  ('prompt_toolkit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats', 'C:\\Program Files\\Python313\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psycopg2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('pure_eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('reportlab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.charts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.areas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\areas.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.legends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\legends.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.piecharts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\piecharts.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.textlabels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\textlabels.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.charts.utils3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\charts\\utils3d.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPDF',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\renderPDF.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPM',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\renderPM.py',
   'PYMODULE'),
  ('reportlab.graphics.renderPS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\renderPS.py',
   'PYMODULE'),
  ('reportlab.graphics.renderSVG',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\renderSVG.py',
   'PYMODULE'),
  ('reportlab.graphics.renderbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\renderbase.py',
   'PYMODULE'),
  ('reportlab.graphics.shapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\shapes.py',
   'PYMODULE'),
  ('reportlab.graphics.testshapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\testshapes.py',
   'PYMODULE'),
  ('reportlab.graphics.transform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\transform.py',
   'PYMODULE'),
  ('reportlab.graphics.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\utils.py',
   'PYMODULE'),
  ('reportlab.graphics.widgetbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\widgetbase.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\widgets\\__init__.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\widgets\\flags.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\widgets\\markers.py',
   'PYMODULE'),
  ('reportlab.graphics.widgets.signsandsymbols',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\graphics\\widgets\\signsandsymbols.py',
   'PYMODULE'),
  ('reportlab.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\__init__.py',
   'PYMODULE'),
  ('reportlab.lib.PyFontify',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\PyFontify.py',
   'PYMODULE'),
  ('reportlab.lib.abag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\abag.py',
   'PYMODULE'),
  ('reportlab.lib.arciv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\arciv.py',
   'PYMODULE'),
  ('reportlab.lib.attrmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\attrmap.py',
   'PYMODULE'),
  ('reportlab.lib.boxstuff',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\boxstuff.py',
   'PYMODULE'),
  ('reportlab.lib.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\colors.py',
   'PYMODULE'),
  ('reportlab.lib.corp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\corp.py',
   'PYMODULE'),
  ('reportlab.lib.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\enums.py',
   'PYMODULE'),
  ('reportlab.lib.fonts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\fonts.py',
   'PYMODULE'),
  ('reportlab.lib.formatters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\formatters.py',
   'PYMODULE'),
  ('reportlab.lib.geomutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\geomutils.py',
   'PYMODULE'),
  ('reportlab.lib.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\logger.py',
   'PYMODULE'),
  ('reportlab.lib.normalDate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\normalDate.py',
   'PYMODULE'),
  ('reportlab.lib.pagesizes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\pagesizes.py',
   'PYMODULE'),
  ('reportlab.lib.pdfencrypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\pdfencrypt.py',
   'PYMODULE'),
  ('reportlab.lib.randomtext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\randomtext.py',
   'PYMODULE'),
  ('reportlab.lib.rl_accel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\rl_accel.py',
   'PYMODULE'),
  ('reportlab.lib.rl_safe_eval',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\rl_safe_eval.py',
   'PYMODULE'),
  ('reportlab.lib.rltempfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\rltempfile.py',
   'PYMODULE'),
  ('reportlab.lib.sequencer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\sequencer.py',
   'PYMODULE'),
  ('reportlab.lib.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\styles.py',
   'PYMODULE'),
  ('reportlab.lib.textsplit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\textsplit.py',
   'PYMODULE'),
  ('reportlab.lib.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\units.py',
   'PYMODULE'),
  ('reportlab.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\utils.py',
   'PYMODULE'),
  ('reportlab.lib.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\lib\\validators.py',
   'PYMODULE'),
  ('reportlab.pdfbase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macexpert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macexpert.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_macroman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_macroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_pdfdoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_pdfdoc.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_standard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_standard.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_symbol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_winansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_winansi.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_enc_zapfdingbats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_enc_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courier.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierbold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courierboldoblique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courierboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_courieroblique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_courieroblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helvetica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helvetica.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticabold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticabold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaboldoblique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaboldoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_helveticaoblique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_helveticaoblique.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_symbol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_symbol.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbold',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbold.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesbolditalic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesbolditalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesitalic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesitalic.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_timesroman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_timesroman.py',
   'PYMODULE'),
  ('reportlab.pdfbase._fontdata_widths_zapfdingbats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_fontdata_widths_zapfdingbats.py',
   'PYMODULE'),
  ('reportlab.pdfbase._glyphlist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\_glyphlist.py',
   'PYMODULE'),
  ('reportlab.pdfbase.acroform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\acroform.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfdoc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\pdfdoc.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfmetrics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\pdfmetrics.py',
   'PYMODULE'),
  ('reportlab.pdfbase.pdfutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\pdfutils.py',
   'PYMODULE'),
  ('reportlab.pdfbase.rl_codecs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\rl_codecs.py',
   'PYMODULE'),
  ('reportlab.pdfbase.ttfonts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfbase\\ttfonts.py',
   'PYMODULE'),
  ('reportlab.pdfgen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\__init__.py',
   'PYMODULE'),
  ('reportlab.pdfgen.canvas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\canvas.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pathobject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\pathobject.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfgeom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\pdfgeom.py',
   'PYMODULE'),
  ('reportlab.pdfgen.pdfimages',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\pdfimages.py',
   'PYMODULE'),
  ('reportlab.pdfgen.textobject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\pdfgen\\textobject.py',
   'PYMODULE'),
  ('reportlab.platypus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\__init__.py',
   'PYMODULE'),
  ('reportlab.platypus.doctemplate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\doctemplate.py',
   'PYMODULE'),
  ('reportlab.platypus.flowables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\flowables.py',
   'PYMODULE'),
  ('reportlab.platypus.frames',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\frames.py',
   'PYMODULE'),
  ('reportlab.platypus.multicol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\multicol.py',
   'PYMODULE'),
  ('reportlab.platypus.paragraph',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\paragraph.py',
   'PYMODULE'),
  ('reportlab.platypus.paraparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\paraparser.py',
   'PYMODULE'),
  ('reportlab.platypus.tables',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\tables.py',
   'PYMODULE'),
  ('reportlab.platypus.xpreformatted',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\platypus\\xpreformatted.py',
   'PYMODULE'),
  ('reportlab.rl_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\rl_config.py',
   'PYMODULE'),
  ('reportlab.rl_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\reportlab\\rl_settings.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Program Files\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\Program Files\\Python313\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Program Files\\Python313\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlalchemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('stack_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program Files\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program Files\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('timeit', 'C:\\Program Files\\Python313\\Lib\\timeit.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Program Files\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('token', 'C:\\Program Files\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Program Files\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tornado',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\_locale_data.py',
   'PYMODULE'),
  ('tornado.autoreload',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\httpclient.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\locale.py',
   'PYMODULE'),
  ('tornado.locks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\routing.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\tcpclient.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\template.py',
   'PYMODULE'),
  ('tornado.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\web.py',
   'PYMODULE'),
  ('tornado.websocket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tornado\\websocket.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('translations',
   'C:\\Users\\<USER>\\Desktop\\027\\translations.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program Files\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program Files\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('wave', 'C:\\Program Files\\Python313\\Lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wtforms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\csrf\\__init__.py',
   'PYMODULE'),
  ('wtforms.csrf.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\csrf\\core.py',
   'PYMODULE'),
  ('wtforms.csrf.session',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\csrf\\session.py',
   'PYMODULE'),
  ('wtforms.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\__init__.py',
   'PYMODULE'),
  ('wtforms.fields.choices',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\choices.py',
   'PYMODULE'),
  ('wtforms.fields.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\core.py',
   'PYMODULE'),
  ('wtforms.fields.datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\datetime.py',
   'PYMODULE'),
  ('wtforms.fields.form',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\form.py',
   'PYMODULE'),
  ('wtforms.fields.list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\list.py',
   'PYMODULE'),
  ('wtforms.fields.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\numeric.py',
   'PYMODULE'),
  ('wtforms.fields.simple',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\fields\\simple.py',
   'PYMODULE'),
  ('wtforms.form',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\form.py',
   'PYMODULE'),
  ('wtforms.i18n',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\i18n.py',
   'PYMODULE'),
  ('wtforms.meta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\meta.py',
   'PYMODULE'),
  ('wtforms.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\utils.py',
   'PYMODULE'),
  ('wtforms.validators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\validators.py',
   'PYMODULE'),
  ('wtforms.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\widgets\\__init__.py',
   'PYMODULE'),
  ('wtforms.widgets.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\wtforms\\widgets\\core.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program Files\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program Files\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('zmq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\__init__.py',
   'PYMODULE'),
  ('zmq._future',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\_future.py',
   'PYMODULE'),
  ('zmq._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\_typing.py',
   'PYMODULE'),
  ('zmq.asyncio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\asyncio.py',
   'PYMODULE'),
  ('zmq.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\backend\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\backend\\cython\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\backend\\select.py',
   'PYMODULE'),
  ('zmq.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\constants.py',
   'PYMODULE'),
  ('zmq.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\error.py',
   'PYMODULE'),
  ('zmq.eventloop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\eventloop\\__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\eventloop\\zmqstream.py',
   'PYMODULE'),
  ('zmq.green',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\green\\__init__.py',
   'PYMODULE'),
  ('zmq.green.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\green\\core.py',
   'PYMODULE'),
  ('zmq.green.device',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\green\\device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\green\\poll.py',
   'PYMODULE'),
  ('zmq.sugar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\__init__.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\context.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\frame.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\poll.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\socket.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\tracker.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\sugar\\version.py',
   'PYMODULE'),
  ('zmq.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\utils\\__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\utils\\garbage.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\utils\\interop.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zmq\\utils\\jsonapi.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Program Files\\Python313\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Program Files\\Python313\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Program Files\\Python313\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Program Files\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
