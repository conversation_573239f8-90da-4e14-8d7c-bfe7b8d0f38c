from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date
from sqlalchemy import func, extract
import os
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill

# استيراد النماذج والنماذج
from models import db, User, Worker, Salary
from forms import LoginForm, WorkerForm, SalaryForm, ReportForm, UserForm, ChangePasswordForm
from config import Config

# استيراد Optional بطريقة آمنة
try:
    from wtforms.validators import Optional, Length
except ImportError:
    from wtforms.validators import Length
    class Optional:
        def __call__(self, form, field):
            pass

app = Flask(__name__)
app.config.from_object(Config)

# تهيئة قاعدة البيانات
db.init_app(app)

def create_default_user():
    """إنشاء مستخدم افتراضي"""
    with app.app_context():
        if not User.query.first():
            admin = User(
                username='admin',
                full_name='مدير النظام',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")

# إزالة الديكوريتر المهجور
# @app.before_first_request
def create_tables():
    """إنشاء الجداول وإضافة البيانات الافتراضية"""
    db.create_all()
    create_default_user()

def login_required(f):
    """ديكوريتر للتحقق من تسجيل الدخول"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            session['user_id'] = user.id
            session['username'] = user.username
            session['full_name'] = user.full_name
            session['user_role'] = user.role
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم"""
    # إحصائيات عامة
    total_workers = Worker.query.count()
    active_workers = Worker.query.filter_by(status='active').count()

    current_month = datetime.now().month
    current_year = datetime.now().year

    current_month_salaries = Salary.query.filter_by(
        month=current_month,
        year=current_year
    ).count()

    unpaid_salaries = Salary.query.filter_by(payment_status='unpaid').count()

    # إجمالي الرواتب لهذا الشهر
    total_monthly_amount = db.session.query(func.sum(Salary.net_salary)).filter_by(
        month=current_month,
        year=current_year
    ).scalar() or 0

    # آخر الرواتب المضافة
    recent_salaries = Salary.query.order_by(Salary.created_at.desc()).limit(5).all()

    # ملخص شهري - طريقة مبسطة
    monthly_summary_raw = db.session.query(
        Salary.month,
        Salary.year,
        func.count(Salary.id).label('worker_count'),
        func.sum(Salary.net_salary).label('total_amount')
    ).group_by(Salary.year, Salary.month).order_by(Salary.year.desc(), Salary.month.desc()).limit(6).all()

    # حساب المبالغ المدفوعة وغير المدفوعة لكل شهر
    monthly_summary = []
    for summary in monthly_summary_raw:
        # حساب المبلغ المدفوع لهذا الشهر
        paid_amount = db.session.query(func.sum(Salary.net_salary)).filter(
            Salary.month == summary.month,
            Salary.year == summary.year,
            Salary.payment_status == 'paid'
        ).scalar() or 0

        # حساب المبلغ غير المدفوع لهذا الشهر
        unpaid_amount = db.session.query(func.sum(Salary.net_salary)).filter(
            Salary.month == summary.month,
            Salary.year == summary.year,
            Salary.payment_status == 'unpaid'
        ).scalar() or 0

        # إنشاء كائن يحتوي على جميع البيانات
        class MonthlySummary:
            def __init__(self, month, year, worker_count, total_amount, paid_amount, unpaid_amount):
                self.month = month
                self.year = year
                self.worker_count = worker_count
                self.total_amount = total_amount
                self.paid_amount = paid_amount
                self.unpaid_amount = unpaid_amount

        monthly_summary.append(MonthlySummary(
            summary.month, summary.year, summary.worker_count,
            summary.total_amount, paid_amount, unpaid_amount
        ))

    # إضافة أسماء الشهور والنسب المئوية
    months = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
        5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
        9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }

    for summary in monthly_summary:
        summary.month_name = months.get(summary.month, 'غير محدد')
        if summary.total_amount > 0:
            summary.paid_percentage = (summary.paid_amount / summary.total_amount) * 100
        else:
            summary.paid_percentage = 0

    stats = {
        'total_workers': total_workers,
        'active_workers': active_workers,
        'current_month_salaries': current_month_salaries,
        'unpaid_salaries': unpaid_salaries,
        'total_monthly_amount': total_monthly_amount
    }

    return render_template('dashboard.html',
                         stats=stats,
                         recent_salaries=recent_salaries,
                         monthly_summary=monthly_summary,
                         current_date=datetime.now())

@app.route('/workers')
@login_required
def workers():
    """عرض قائمة العمال"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    status_filter = request.args.get('status', 'all', type=str)

    query = Worker.query

    if search:
        query = query.filter(
            (Worker.name.contains(search)) |
            (Worker.nationality.contains(search)) |
            (Worker.job_type.contains(search)) |
            (Worker.id_number.contains(search))
        )

    if status_filter != 'all':
        query = query.filter_by(status=status_filter)

    workers = query.order_by(Worker.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('workers.html', workers=workers, search=search, status_filter=status_filter)

@app.route('/workers/add', methods=['GET', 'POST'])
@login_required
def add_worker():
    """إضافة عامل جديد"""
    form = WorkerForm()
    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم الهوية
        existing_worker = Worker.query.filter_by(id_number=form.id_number.data).first()
        if existing_worker:
            flash('رقم الهوية موجود مسبقاً', 'error')
            return render_template('worker_form.html', form=form, title='إضافة عامل جديد')

        worker = Worker(
            name=form.name.data,
            nationality=form.nationality.data,
            job_type=form.job_type.data,
            id_number=form.id_number.data,
            phone=form.phone.data,
            hire_date=form.hire_date.data,
            basic_salary=form.basic_salary.data,
            status=form.status.data
        )

        db.session.add(worker)
        db.session.commit()
        flash('تم إضافة العامل بنجاح', 'success')
        return redirect(url_for('workers'))

    return render_template('worker_form.html', form=form, title='إضافة عامل جديد')

@app.route('/workers/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_worker(id):
    """تعديل بيانات العامل"""
    worker = Worker.query.get_or_404(id)
    form = WorkerForm(obj=worker)

    if form.validate_on_submit():
        # التحقق من عدم تكرار رقم الهوية (باستثناء العامل الحالي)
        existing_worker = Worker.query.filter(
            Worker.id_number == form.id_number.data,
            Worker.id != id
        ).first()

        if existing_worker:
            flash('رقم الهوية موجود مسبقاً', 'error')
            return render_template('worker_form.html', form=form, title='تعديل بيانات العامل', worker=worker)

        worker.name = form.name.data
        worker.nationality = form.nationality.data
        worker.job_type = form.job_type.data
        worker.id_number = form.id_number.data
        worker.phone = form.phone.data
        worker.hire_date = form.hire_date.data
        worker.basic_salary = form.basic_salary.data
        worker.status = form.status.data
        worker.updated_at = datetime.utcnow()

        db.session.commit()
        flash('تم تحديث بيانات العامل بنجاح', 'success')
        return redirect(url_for('workers'))

    return render_template('worker_form.html', form=form, title='تعديل بيانات العامل', worker=worker)

@app.route('/workers/delete/<int:id>', methods=['POST'])
@login_required
def delete_worker(id):
    """حذف العامل"""
    worker = Worker.query.get_or_404(id)

    # التحقق من وجود رواتب مرتبطة
    if worker.salaries:
        flash('لا يمكن حذف العامل لوجود رواتب مرتبطة به', 'error')
        return redirect(url_for('workers'))

    db.session.delete(worker)
    db.session.commit()
    flash('تم حذف العامل بنجاح', 'success')
    return redirect(url_for('workers'))

@app.route('/salaries')
@login_required
def salaries():
    """عرض قائمة الرواتب"""
    page = request.args.get('page', 1, type=int)
    month_filter = request.args.get('month', 0, type=int)
    year_filter = request.args.get('year', datetime.now().year, type=int)
    status_filter = request.args.get('status', 'all', type=str)

    query = Salary.query

    if month_filter > 0:
        query = query.filter_by(month=month_filter)

    if year_filter:
        query = query.filter_by(year=year_filter)

    if status_filter != 'all':
        query = query.filter_by(payment_status=status_filter)

    salaries = query.order_by(Salary.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )

    return render_template('salaries.html',
                         salaries=salaries,
                         month_filter=month_filter,
                         year_filter=year_filter,
                         status_filter=status_filter)

@app.route('/salaries/add', methods=['GET', 'POST'])
@login_required
def add_salary():
    """إضافة راتب جديد"""
    form = SalaryForm()

    # تحديث قائمة العمال النشطين
    active_workers = Worker.query.filter_by(status='active').all()
    form.worker_id.choices = [(w.id, f"{w.name} - {w.job_type}") for w in active_workers]

    if form.validate_on_submit():
        # التحقق من عدم تكرار الراتب لنفس العامل في نفس الشهر
        existing_salary = Salary.query.filter_by(
            worker_id=form.worker_id.data,
            month=form.month.data,
            year=form.year.data
        ).first()

        if existing_salary:
            flash('راتب هذا العامل لهذا الشهر موجود مسبقاً', 'error')
            return render_template('salary_form.html', form=form, title='إضافة راتب جديد')

        salary = Salary(
            worker_id=form.worker_id.data,
            month=form.month.data,
            year=form.year.data,
            basic_salary=form.basic_salary.data,
            allowances=form.allowances.data or 0,
            deductions=form.deductions.data or 0,
            payment_status=form.payment_status.data,
            payment_date=form.payment_date.data,
            notes=form.notes.data
        )

        salary.calculate_net_salary()

        db.session.add(salary)
        db.session.commit()
        flash('تم إضافة الراتب بنجاح', 'success')
        return redirect(url_for('salaries'))

    return render_template('salary_form.html', form=form, title='إضافة راتب جديد')

@app.route('/salaries/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_salary(id):
    """تعديل الراتب"""
    salary = Salary.query.get_or_404(id)
    form = SalaryForm(obj=salary)

    # تحديث قائمة العمال النشطين
    active_workers = Worker.query.filter_by(status='active').all()
    form.worker_id.choices = [(w.id, f"{w.name} - {w.job_type}") for w in active_workers]

    if form.validate_on_submit():
        # التحقق من عدم تكرار الراتب (باستثناء الراتب الحالي)
        existing_salary = Salary.query.filter(
            Salary.worker_id == form.worker_id.data,
            Salary.month == form.month.data,
            Salary.year == form.year.data,
            Salary.id != id
        ).first()

        if existing_salary:
            flash('راتب هذا العامل لهذا الشهر موجود مسبقاً', 'error')
            return render_template('salary_form.html', form=form, title='تعديل الراتب', salary=salary)

        salary.worker_id = form.worker_id.data
        salary.month = form.month.data
        salary.year = form.year.data
        salary.basic_salary = form.basic_salary.data
        salary.allowances = form.allowances.data or 0
        salary.deductions = form.deductions.data or 0
        salary.payment_status = form.payment_status.data
        salary.payment_date = form.payment_date.data
        salary.notes = form.notes.data
        salary.updated_at = datetime.utcnow()

        salary.calculate_net_salary()

        db.session.commit()
        flash('تم تحديث الراتب بنجاح', 'success')
        return redirect(url_for('salaries'))

    return render_template('salary_form.html', form=form, title='تعديل الراتب', salary=salary)

@app.route('/salaries/delete/<int:id>', methods=['POST'])
@login_required
def delete_salary(id):
    """حذف الراتب"""
    salary = Salary.query.get_or_404(id)
    db.session.delete(salary)
    db.session.commit()
    flash('تم حذف الراتب بنجاح', 'success')
    return redirect(url_for('salaries'))

@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير"""
    form = ReportForm()
    return render_template('reports.html', form=form)

# ==================== إدارة المستخدمين ====================

@app.route('/users')
@login_required
def users():
    """عرض قائمة المستخدمين"""
    # التحقق من صلاحية المدير
    current_user = User.query.get(session['user_id'])
    if current_user.role not in ['admin']:
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    role_filter = request.args.get('role', 'all', type=str)

    query = User.query

    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.full_name.contains(search))
        )

    if role_filter != 'all':
        query = query.filter_by(role=role_filter)

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('users.html', users=users, search=search, role_filter=role_filter)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    """إضافة مستخدم جديد"""
    # التحقق من صلاحية المدير
    current_user = User.query.get(session['user_id'])
    if current_user.role not in ['admin']:
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    form = UserForm()
    if form.validate_on_submit():
        # التحقق من عدم تكرار اسم المستخدم
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('user_form.html', form=form, title='إضافة مستخدم جديد')

        if not form.password.data:
            flash('كلمة المرور مطلوبة للمستخدم الجديد', 'error')
            return render_template('user_form.html', form=form, title='إضافة مستخدم جديد')

        user = User(
            username=form.username.data,
            full_name=form.full_name.data,
            role=form.role.data,
            is_active=form.is_active.data == 'True' if isinstance(form.is_active.data, str) else form.is_active.data
        )
        user.set_password(form.password.data)

        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('user_form.html', form=form, title='إضافة مستخدم جديد')

@app.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    """تعديل بيانات المستخدم"""
    # التحقق من صلاحية المدير
    current_user = User.query.get(session['user_id'])
    if current_user.role not in ['admin']:
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)
    form = UserForm(obj=user)

    # تعيين القيم الصحيحة للنموذج
    form.is_active.data = 'True' if user.is_active else 'False'

    # إزالة متطلب كلمة المرور للتعديل
    form.password.validators = [Optional(), Length(min=4)]

    if form.validate_on_submit():
        # التحقق من عدم تكرار اسم المستخدم (باستثناء المستخدم الحالي)
        existing_user = User.query.filter(
            User.username == form.username.data,
            User.id != id
        ).first()

        if existing_user:
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('user_form.html', form=form, title='تعديل بيانات المستخدم', user=user)

        user.username = form.username.data
        user.full_name = form.full_name.data
        user.role = form.role.data
        user.is_active = form.is_active.data == 'True' if isinstance(form.is_active.data, str) else form.is_active.data

        # تحديث كلمة المرور إذا تم إدخالها
        if form.password.data:
            user.set_password(form.password.data)

        db.session.commit()
        flash('تم تحديث بيانات المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('user_form.html', form=form, title='تعديل بيانات المستخدم', user=user)

@app.route('/users/delete/<int:id>', methods=['POST'])
@login_required
def delete_user(id):
    """حذف المستخدم"""
    # التحقق من صلاحية المدير
    current_user = User.query.get(session['user_id'])
    if current_user.role not in ['admin']:
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)

    # منع حذف المستخدم الحالي
    if user.id == session['user_id']:
        flash('لا يمكن حذف المستخدم الحالي', 'error')
        return redirect(url_for('users'))

    # منع حذف آخر مدير
    if user.role == 'admin':
        admin_count = User.query.filter_by(role='admin', is_active=True).count()
        if admin_count <= 1:
            flash('لا يمكن حذف آخر مدير في النظام', 'error')
            return redirect(url_for('users'))

    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/profile')
@login_required
def profile():
    """صفحة الملف الشخصي"""
    user = User.query.get(session['user_id'])
    return render_template('profile.html', user=user)

@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    form = ChangePasswordForm()
    user = User.query.get(session['user_id'])

    if form.validate_on_submit():
        if not user.check_password(form.current_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return render_template('change_password.html', form=form)

        user.set_password(form.new_password.data)
        db.session.commit()
        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('profile'))

    return render_template('change_password.html', form=form)

@app.route('/reports/preview', methods=['POST'])
@login_required
def preview_report():
    """معاينة التقرير"""
    form = ReportForm()
    if form.validate_on_submit():
        query = Salary.query

        if form.month.data > 0:
            query = query.filter_by(month=form.month.data)

        if form.year.data:
            query = query.filter_by(year=form.year.data)

        if form.payment_status.data != 'all':
            query = query.filter_by(payment_status=form.payment_status.data)

        salaries = query.order_by(Salary.created_at.desc()).all()

        # تحضير البيانات
        salaries_data = []
        total_amount = 0
        paid_amount = 0
        unpaid_amount = 0

        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }

        for salary in salaries:
            salaries_data.append({
                'worker_name': salary.worker.name,
                'month_name': months.get(salary.month, 'غير محدد'),
                'year': salary.year,
                'basic_salary': salary.basic_salary,
                'allowances': salary.allowances,
                'deductions': salary.deductions,
                'net_salary': salary.net_salary,
                'payment_status': salary.payment_status
            })

            total_amount += salary.net_salary
            if salary.payment_status == 'paid':
                paid_amount += salary.net_salary
            else:
                unpaid_amount += salary.net_salary

        summary = {
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'unpaid_amount': unpaid_amount,
            'worker_count': len(salaries)
        }

        statistics = {
            'total_workers': Worker.query.count(),
            'total_salaries': total_amount,
            'paid_amount': paid_amount,
            'unpaid_amount': unpaid_amount
        }

        return jsonify({
            'salaries': salaries_data,
            'summary': summary,
            'statistics': statistics
        })

    return jsonify({'error': 'بيانات غير صحيحة'}), 400

@app.route('/reports/generate', methods=['POST'])
@login_required
def generate_report():
    """إنشاء وتصدير التقرير"""
    form = ReportForm()
    report_format = request.form.get('format', 'pdf')

    if form.validate_on_submit():
        query = Salary.query

        if form.month.data > 0:
            query = query.filter_by(month=form.month.data)

        if form.year.data:
            query = query.filter_by(year=form.year.data)

        if form.payment_status.data != 'all':
            query = query.filter_by(payment_status=form.payment_status.data)

        salaries = query.order_by(Salary.worker_id, Salary.month).all()

        if report_format == 'pdf':
            return generate_pdf_report(salaries, form)
        else:
            return generate_excel_report(salaries, form)

    flash('بيانات التقرير غير صحيحة', 'error')
    return redirect(url_for('reports'))

def generate_pdf_report(salaries, form):
    """إنشاء تقرير PDF"""
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont

    buffer = BytesIO()

    # إنشاء المستند
    doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=0.5*inch,
                           leftMargin=0.5*inch, topMargin=1*inch, bottomMargin=0.5*inch)

    # قائمة العناصر
    elements = []

    # العنوان
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=getSampleStyleSheet()['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1,  # وسط
        textColor=colors.darkblue
    )

    months = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
        5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
        9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }

    if form.month.data > 0:
        month_name = months.get(form.month.data, 'غير محدد')
        title = f"تقرير رواتب العمالة المنزلية - {month_name} {form.year.data}"
    else:
        title = f"تقرير رواتب العمالة المنزلية - {form.year.data}"

    elements.append(Paragraph(title, title_style))
    elements.append(Spacer(1, 20))

    # إعداد البيانات للجدول
    data = [['العامل', 'الشهر/السنة', 'الراتب الأساسي', 'البدلات', 'الخصومات', 'صافي الراتب', 'الحالة']]

    total_amount = 0
    paid_amount = 0
    unpaid_amount = 0

    for salary in salaries:
        month_name = months.get(salary.month, 'غير محدد')
        status = 'مدفوع' if salary.payment_status == 'paid' else 'غير مدفوع'

        data.append([
            salary.worker.name,
            f"{month_name} {salary.year}",
            f"{salary.basic_salary:,.0f}",
            f"{salary.allowances:,.0f}",
            f"{salary.deductions:,.0f}",
            f"{salary.net_salary:,.0f}",
            status
        ])

        total_amount += salary.net_salary
        if salary.payment_status == 'paid':
            paid_amount += salary.net_salary
        else:
            unpaid_amount += salary.net_salary

    # إضافة صف الإجمالي
    data.append(['', '', '', '', 'الإجمالي:', f"{total_amount:,.0f}", ''])

    # إنشاء الجدول
    table = Table(data, colWidths=[1.5*inch, 1.2*inch, 1*inch, 0.8*inch, 0.8*inch, 1*inch, 0.8*inch])

    # تنسيق الجدول
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -2), colors.beige),
        ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(table)
    elements.append(Spacer(1, 20))

    # ملخص الإحصائيات
    summary_data = [
        ['إجمالي الرواتب', f"{total_amount:,.0f} ريال"],
        ['المبلغ المدفوع', f"{paid_amount:,.0f} ريال"],
        ['المبلغ غير المدفوع', f"{unpaid_amount:,.0f} ريال"],
        ['عدد العمال', str(len(salaries))]
    ]

    summary_table = Table(summary_data, colWidths=[2*inch, 2*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), colors.lightblue),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(summary_table)

    # بناء المستند
    doc.build(elements)

    buffer.seek(0)

    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'attachment; filename=salary_report_{form.year.data}.pdf'

    return response

def generate_excel_report(salaries, form):
    """إنشاء تقرير Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter

    wb = Workbook()
    ws = wb.active

    months = {
        1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
        5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
        9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    }

    if form.month.data > 0:
        month_name = months.get(form.month.data, 'غير محدد')
        ws.title = f"رواتب {month_name} {form.year.data}"
    else:
        ws.title = f"رواتب {form.year.data}"

    # العنوان
    if form.month.data > 0:
        month_name = months.get(form.month.data, 'غير محدد')
        title = f"تقرير رواتب العمالة المنزلية - {month_name} {form.year.data}"
    else:
        title = f"تقرير رواتب العمالة المنزلية - {form.year.data}"

    ws.merge_cells('A1:G1')
    ws['A1'] = title
    ws['A1'].font = Font(size=16, bold=True, color='FFFFFF')
    ws['A1'].fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 30

    # رؤوس الأعمدة
    headers = ['العامل', 'الشهر/السنة', 'الراتب الأساسي', 'البدلات', 'الخصومات', 'صافي الراتب', 'الحالة']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=2, column=col, value=header)
        cell.font = Font(bold=True, color='FFFFFF')
        cell.fill = PatternFill(start_color='4F81BD', end_color='4F81BD', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # البيانات
    total_amount = 0
    paid_amount = 0
    unpaid_amount = 0

    for row, salary in enumerate(salaries, 3):
        month_name = months.get(salary.month, 'غير محدد')
        status = 'مدفوع' if salary.payment_status == 'paid' else 'غير مدفوع'

        ws.cell(row=row, column=1, value=salary.worker.name)
        ws.cell(row=row, column=2, value=f"{month_name} {salary.year}")
        ws.cell(row=row, column=3, value=salary.basic_salary)
        ws.cell(row=row, column=4, value=salary.allowances)
        ws.cell(row=row, column=5, value=salary.deductions)
        ws.cell(row=row, column=6, value=salary.net_salary)
        ws.cell(row=row, column=7, value=status)

        # تنسيق الأرقام
        for col in [3, 4, 5, 6]:
            ws.cell(row=row, column=col).number_format = '#,##0'

        total_amount += salary.net_salary
        if salary.payment_status == 'paid':
            paid_amount += salary.net_salary
        else:
            unpaid_amount += salary.net_salary

    # صف الإجمالي
    total_row = len(salaries) + 3
    ws.cell(row=total_row, column=5, value='الإجمالي:').font = Font(bold=True)
    ws.cell(row=total_row, column=6, value=total_amount).font = Font(bold=True)
    ws.cell(row=total_row, column=6).number_format = '#,##0'

    # تنسيق الجدول
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    for row in ws.iter_rows(min_row=2, max_row=total_row, min_col=1, max_col=7):
        for cell in row:
            cell.border = thin_border
            cell.alignment = Alignment(horizontal='center', vertical='center')

    # تعديل عرض الأعمدة
    column_widths = [20, 15, 15, 12, 12, 15, 12]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(col)].width = width

    # إضافة ملخص الإحصائيات
    summary_start_row = total_row + 3
    ws.cell(row=summary_start_row, column=1, value='ملخص الإحصائيات').font = Font(size=14, bold=True)

    summary_data = [
        ['إجمالي الرواتب:', f"{total_amount:,.0f} ريال"],
        ['المبلغ المدفوع:', f"{paid_amount:,.0f} ريال"],
        ['المبلغ غير المدفوع:', f"{unpaid_amount:,.0f} ريال"],
        ['عدد العمال:', str(len(salaries))]
    ]

    for i, (label, value) in enumerate(summary_data):
        row = summary_start_row + i + 1
        ws.cell(row=row, column=1, value=label).font = Font(bold=True)
        ws.cell(row=row, column=2, value=value)

    # حفظ الملف في الذاكرة
    buffer = BytesIO()
    wb.save(buffer)
    buffer.seek(0)

    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = f'attachment; filename=salary_report_{form.year.data}.xlsx'

    return response

@app.route('/api/worker/<int:worker_id>/basic_salary')
@login_required
def get_worker_basic_salary(worker_id):
    """الحصول على الراتب الأساسي للعامل"""
    worker = Worker.query.get_or_404(worker_id)
    return jsonify({'basic_salary': worker.basic_salary})

@app.route('/api/statistics')
@login_required
def get_statistics():
    """الحصول على الإحصائيات العامة"""
    total_workers = Worker.query.count()

    # إجمالي الرواتب لهذا العام
    current_year = datetime.now().year
    total_salaries = db.session.query(func.sum(Salary.net_salary)).filter_by(year=current_year).scalar() or 0

    # المبلغ المدفوع وغير المدفوع
    paid_amount = db.session.query(func.sum(Salary.net_salary)).filter(
        Salary.year == current_year,
        Salary.payment_status == 'paid'
    ).scalar() or 0

    unpaid_amount = db.session.query(func.sum(Salary.net_salary)).filter(
        Salary.year == current_year,
        Salary.payment_status == 'unpaid'
    ).scalar() or 0

    return jsonify({
        'total_workers': total_workers,
        'total_salaries': total_salaries,
        'paid_amount': paid_amount,
        'unpaid_amount': unpaid_amount
    })

@app.route('/salary/receipt/<int:salary_id>')
@login_required
def salary_receipt(salary_id):
    """إيصال الراتب"""
    salary = Salary.query.get_or_404(salary_id)

    buffer = BytesIO()

    # إنشاء مستند PDF للإيصال
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors

    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=1*inch,
                           leftMargin=1*inch, topMargin=1*inch, bottomMargin=1*inch)

    elements = []

    # العنوان
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=getSampleStyleSheet()['Heading1'],
        fontSize=20,
        spaceAfter=30,
        alignment=1,
        textColor=colors.darkblue
    )

    elements.append(Paragraph("إيصال راتب", title_style))
    elements.append(Paragraph(Config.COMPANY_NAME, getSampleStyleSheet()['Heading2']))
    elements.append(Spacer(1, 20))

    # بيانات الإيصال
    receipt_data = [
        ['اسم العامل:', salary.worker.name],
        ['نوع العمل:', salary.worker.job_type],
        ['رقم الهوية/الإقامة/الفيزا:', salary.worker.id_number],
        ['الشهر:', salary.get_month_name()],
        ['السنة:', str(salary.year)],
        ['', ''],
        ['الراتب الأساسي:', f"{salary.basic_salary:,.0f} ر.ق"],
        ['البدلات:', f"{salary.allowances:,.0f} ر.ق"],
        ['الخصومات:', f"{salary.deductions:,.0f} ر.ق"],
        ['صافي الراتب:', f"{salary.net_salary:,.0f} ر.ق"],
        ['', ''],
        ['حالة الدفع:', 'مدفوع' if salary.payment_status == 'paid' else 'غير مدفوع'],
        ['تاريخ الدفع:', salary.payment_date.strftime('%Y/%m/%d') if salary.payment_date else '-'],
    ]

    receipt_table = Table(receipt_data, colWidths=[2*inch, 3*inch])
    receipt_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('BACKGROUND', (0, 6), (-1, 9), colors.lightgrey),
        ('FONTNAME', (0, 9), (-1, 9), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 9), (-1, 9), 14),
    ]))

    elements.append(receipt_table)
    elements.append(Spacer(1, 30))

    # التوقيع
    signature_data = [
        ['توقيع المستلم:', ''],
        ['التاريخ:', datetime.now().strftime('%Y/%m/%d')],
    ]

    signature_table = Table(signature_data, colWidths=[2*inch, 3*inch])
    signature_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
    ]))

    elements.append(signature_table)

    # بناء المستند
    doc.build(elements)

    buffer.seek(0)

    response = make_response(buffer.getvalue())
    response.headers['Content-Type'] = 'application/pdf'

    # استخدام اسم ملف بالإنجليزية لتجنب مشاكل التشفير
    filename = f'receipt_{salary.id}_{salary.month}_{salary.year}.pdf'
    response.headers['Content-Disposition'] = f'inline; filename={filename}'

    return response

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_default_user()
    app.run(debug=True, host='0.0.0.0', port=5000)
