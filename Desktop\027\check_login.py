#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
فحص وإصلاح مشكلة تسجيل الدخول
"""

import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash

def check_and_fix_login():
    """فحص وإصلاح مشكلة تسجيل الدخول"""
    
    print("🔍 فحص قاعدة البيانات...")
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('payroll.db')
    cursor = conn.cursor()
    
    # فحص جدول المستخدمين
    try:
        cursor.execute("SELECT * FROM user")
        users = cursor.fetchall()
        
        print(f"📊 عدد المستخدمين في قاعدة البيانات: {len(users)}")
        
        if users:
            print("\n👥 المستخدمين الموجودين:")
            for user in users:
                print(f"   ID: {user[0]}, اسم المستخدم: {user[1]}, الدور: {user[4]}")
        else:
            print("❌ لا يوجد مستخدمين في قاعدة البيانات")
            
    except sqlite3.Error as e:
        print(f"❌ خطأ في قراءة جدول المستخدمين: {e}")
        return False
    
    # فحص المستخدم الافتراضي
    cursor.execute("SELECT * FROM user WHERE username = ?", ('admin',))
    admin_user = cursor.fetchone()
    
    if admin_user:
        print(f"\n✅ المستخدم admin موجود:")
        print(f"   ID: {admin_user[0]}")
        print(f"   اسم المستخدم: {admin_user[1]}")
        print(f"   الدور: {admin_user[4]}")
        
        # فحص كلمة المرور
        stored_password_hash = admin_user[2]
        test_password = 'admin123'
        
        if check_password_hash(stored_password_hash, test_password):
            print(f"✅ كلمة المرور صحيحة: {test_password}")
        else:
            print(f"❌ كلمة المرور غير صحيحة، سيتم إعادة تعيينها...")
            
            # إعادة تعيين كلمة المرور
            new_password_hash = generate_password_hash('admin123')
            cursor.execute("UPDATE user SET password_hash = ? WHERE username = ?", 
                         (new_password_hash, 'admin'))
            conn.commit()
            print(f"✅ تم إعادة تعيين كلمة المرور إلى: admin123")
            
    else:
        print("❌ المستخدم admin غير موجود، سيتم إنشاؤه...")
        
        # إنشاء المستخدم الافتراضي
        password_hash = generate_password_hash('admin123')
        cursor.execute("""
            INSERT INTO user (username, password_hash, full_name, role) 
            VALUES (?, ?, ?, ?)
        """, ('admin', password_hash, 'مدير النظام', 'admin'))
        conn.commit()
        print("✅ تم إنشاء المستخدم admin بنجاح")
    
    # إنشاء مستخدم تجريبي إضافي
    cursor.execute("SELECT * FROM user WHERE username = ?", ('test',))
    test_user = cursor.fetchone()
    
    if not test_user:
        print("\n📝 إنشاء مستخدم تجريبي...")
        password_hash = generate_password_hash('test123')
        cursor.execute("""
            INSERT INTO user (username, password_hash, full_name, role) 
            VALUES (?, ?, ?, ?)
        """, ('test', password_hash, 'مستخدم تجريبي', 'user'))
        conn.commit()
        print("✅ تم إنشاء المستخدم test بنجاح")
    
    conn.close()
    
    print("\n" + "="*60)
    print("🔑 بيانات تسجيل الدخول:")
    print("="*60)
    print("👤 المستخدم الرئيسي:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("   الدور: مدير النظام")
    print()
    print("👤 المستخدم التجريبي:")
    print("   اسم المستخدم: test")
    print("   كلمة المرور: test123")
    print("   الدور: مستخدم عادي")
    print("="*60)
    print("🌐 رابط النظام: http://localhost:5000")
    print("="*60)
    
    return True

def test_login_form():
    """اختبار نموذج تسجيل الدخول"""
    print("\n🧪 اختبار نموذج تسجيل الدخول...")
    
    try:
        import requests
        
        # اختبار الوصول لصفحة تسجيل الدخول
        response = requests.get('http://localhost:5000/login')
        if response.status_code == 200:
            print("✅ صفحة تسجيل الدخول تعمل بشكل صحيح")
        else:
            print(f"❌ مشكلة في صفحة تسجيل الدخول: {response.status_code}")
            
        # اختبار تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        session = requests.Session()
        login_response = session.post('http://localhost:5000/login', data=login_data)
        
        if login_response.status_code == 200 and 'dashboard' in login_response.url:
            print("✅ تسجيل الدخول يعمل بشكل صحيح")
        else:
            print(f"❌ مشكلة في تسجيل الدخول: {login_response.status_code}")
            
    except ImportError:
        print("⚠️ مكتبة requests غير مثبتة، لا يمكن اختبار تسجيل الدخول تلقائياً")
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")

if __name__ == "__main__":
    print("🔧 أداة فحص وإصلاح مشكلة تسجيل الدخول")
    print("="*60)
    
    if check_and_fix_login():
        test_login_form()
        
        print("\n💡 نصائح لحل مشاكل تسجيل الدخول:")
        print("1. تأكد من أن النظام يعمل على http://localhost:5000")
        print("2. استخدم بيانات الدخول الصحيحة")
        print("3. تأكد من تفعيل JavaScript في المتصفح")
        print("4. جرب مسح cache المتصفح")
        print("5. جرب متصفح آخر")
        print("6. تأكد من عدم وجود برامج حماية تحجب الموقع")
    else:
        print("❌ فشل في إصلاح مشكلة تسجيل الدخول")
