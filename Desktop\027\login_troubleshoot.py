#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
أداة تشخيص شاملة لمشاكل تسجيل الدخول
"""

import sqlite3
import requests
from werkzeug.security import generate_password_hash, check_password_hash

def comprehensive_login_check():
    """فحص شامل لمشاكل تسجيل الدخول"""
    
    print("🔍 بدء التشخيص الشامل لمشاكل تسجيل الدخول")
    print("=" * 60)
    
    # 1. فحص قاعدة البيانات
    print("1️⃣ فحص قاعدة البيانات...")
    db_status = check_database()
    
    # 2. فحص الخادم
    print("\n2️⃣ فحص الخادم...")
    server_status = check_server()
    
    # 3. فحص نموذج تسجيل الدخول
    print("\n3️⃣ فحص نموذج تسجيل الدخول...")
    form_status = check_login_form()
    
    # 4. اختبار تسجيل الدخول
    print("\n4️⃣ اختبار تسجيل الدخول...")
    login_status = test_login()
    
    # 5. تقرير النتائج
    print("\n" + "=" * 60)
    print("📊 تقرير التشخيص:")
    print("=" * 60)
    
    if db_status and server_status and form_status and login_status:
        print("✅ جميع الفحوصات نجحت - النظام يعمل بشكل صحيح")
        print_login_instructions()
    else:
        print("❌ تم اكتشاف مشاكل:")
        if not db_status:
            print("   - مشكلة في قاعدة البيانات")
        if not server_status:
            print("   - مشكلة في الخادم")
        if not form_status:
            print("   - مشكلة في نموذج تسجيل الدخول")
        if not login_status:
            print("   - مشكلة في عملية تسجيل الدخول")
        
        print("\n💡 الحلول المقترحة:")
        print_solutions()

def check_database():
    """فحص قاعدة البيانات"""
    try:
        conn = sqlite3.connect('payroll.db')
        cursor = conn.cursor()
        
        # فحص وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
        if not cursor.fetchone():
            print("❌ جدول المستخدمين غير موجود")
            return False
        
        # فحص المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user")
        user_count = cursor.fetchone()[0]
        
        if user_count == 0:
            print("⚠️ لا يوجد مستخدمين، سيتم إنشاء المستخدم الافتراضي...")
            create_default_user(cursor)
            conn.commit()
        
        # فحص المستخدم admin
        cursor.execute("SELECT * FROM user WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"✅ المستخدم admin موجود (ID: {admin_user[0]})")
            
            # فحص كلمة المرور
            if check_password_hash(admin_user[2], 'admin123'):
                print("✅ كلمة المرور صحيحة")
            else:
                print("❌ كلمة المرور غير صحيحة، سيتم إصلاحها...")
                fix_admin_password(cursor)
                conn.commit()
            
            # فحص حالة المستخدم
            if admin_user[5]:  # is_active
                print("✅ المستخدم نشط")
            else:
                print("❌ المستخدم غير نشط، سيتم تفعيله...")
                cursor.execute("UPDATE user SET is_active = 1 WHERE username = 'admin'")
                conn.commit()
                print("✅ تم تفعيل المستخدم")
        else:
            print("❌ المستخدم admin غير موجود، سيتم إنشاؤه...")
            create_default_user(cursor)
            conn.commit()
        
        conn.close()
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def create_default_user(cursor):
    """إنشاء المستخدم الافتراضي"""
    password_hash = generate_password_hash('admin123')
    cursor.execute("""
        INSERT OR REPLACE INTO user (username, password_hash, full_name, role, is_active) 
        VALUES (?, ?, ?, ?, ?)
    """, ('admin', password_hash, 'مدير النظام', 'admin', True))
    print("✅ تم إنشاء المستخدم admin")

def fix_admin_password(cursor):
    """إصلاح كلمة مرور المدير"""
    password_hash = generate_password_hash('admin123')
    cursor.execute("UPDATE user SET password_hash = ? WHERE username = 'admin'", (password_hash,))
    print("✅ تم إصلاح كلمة مرور admin")

def check_server():
    """فحص الخادم"""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ الخادم يرد بكود خطأ: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم")
        print("💡 تأكد من تشغيل النظام باستخدام: python run.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال بالخادم: {e}")
        return False

def check_login_form():
    """فحص نموذج تسجيل الدخول"""
    try:
        response = requests.get('http://localhost:5000/login', timeout=5)
        if response.status_code == 200:
            if 'form' in response.text and 'username' in response.text and 'password' in response.text:
                print("✅ نموذج تسجيل الدخول يعمل بشكل صحيح")
                return True
            else:
                print("❌ نموذج تسجيل الدخول غير مكتمل")
                return False
        else:
            print(f"❌ صفحة تسجيل الدخول ترد بكود خطأ: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في فحص نموذج تسجيل الدخول: {e}")
        return False

def test_login():
    """اختبار عملية تسجيل الدخول"""
    try:
        session = requests.Session()
        
        # الحصول على صفحة تسجيل الدخول أولاً
        login_page = session.get('http://localhost:5000/login')
        
        # استخراج CSRF token إذا كان موجود
        csrf_token = None
        if 'csrf_token' in login_page.text:
            import re
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', login_page.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
        
        # بيانات تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        # محاولة تسجيل الدخول
        response = session.post('http://localhost:5000/login', data=login_data, allow_redirects=False)
        
        if response.status_code == 302:  # إعادة توجيه
            if 'dashboard' in response.headers.get('Location', ''):
                print("✅ تسجيل الدخول نجح")
                return True
            else:
                print("❌ تسجيل الدخول فشل - إعادة توجيه خاطئة")
                return False
        elif response.status_code == 200:
            if 'dashboard' in response.url or 'لوحة التحكم' in response.text:
                print("✅ تسجيل الدخول نجح")
                return True
            else:
                print("❌ تسجيل الدخول فشل - بيانات خاطئة")
                return False
        else:
            print(f"❌ تسجيل الدخول فشل - كود الاستجابة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def print_login_instructions():
    """طباعة تعليمات تسجيل الدخول"""
    print("\n🔑 تعليمات تسجيل الدخول:")
    print("=" * 40)
    print("🌐 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔒 كلمة المرور: admin123")
    print("=" * 40)

def print_solutions():
    """طباعة الحلول المقترحة"""
    print("1. تأكد من تشغيل النظام: python run.py")
    print("2. تأكد من الرابط الصحيح: http://localhost:5000")
    print("3. استخدم بيانات الدخول الصحيحة: admin / admin123")
    print("4. امسح cache المتصفح وأعد المحاولة")
    print("5. جرب متصفح آخر")
    print("6. تأكد من عدم حجب برامج الحماية للموقع")
    print("7. أعد تشغيل النظام إذا لزم الأمر")

if __name__ == "__main__":
    comprehensive_login_check()
