@echo off
chcp 65001 >nul
title إيقاف خدمة نظام الرواتب

echo ============================================================
echo 🛑 إيقاف خدمة نظام صرف رواتب العمالة المنزلية
echo ============================================================

echo 🔍 البحث عن العمليات النشطة...
tasklist | findstr "PayrollSystem.exe" >nul
if %errorlevel%==0 (
    echo ✅ تم العثور على الخدمة النشطة
    echo 🛑 إيقاف الخدمة...
    taskkill /f /im "PayrollSystem.exe" >nul 2>&1
    echo ✅ تم إيقاف الخدمة بنجاح!
) else (
    echo ❌ الخدمة غير نشطة
)

echo ============================================================
pause
