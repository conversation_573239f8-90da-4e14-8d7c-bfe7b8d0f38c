{% extends "base.html" %}

{% block title %}{{ title }} - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-user-edit me-2"></i>{{ title }}</h2>
            <p class="text-muted mb-0">إدخال وتعديل بيانات العامل</p>
        </div>
        <a href="{{ url_for('workers') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات العامل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control") }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الجنسية -->
                        <div class="col-md-6 mb-3">
                            {{ form.nationality.label(class="form-label") }}
                            {{ form.nationality(class="form-select") }}
                            {% if form.nationality.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.nationality.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- نوع العمل -->
                        <div class="col-md-6 mb-3">
                            {{ form.job_type.label(class="form-label") }}
                            {{ form.job_type(class="form-select") }}
                            {% if form.job_type.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.job_type.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- رقم الهوية/الإقامة/الفيزا -->
                        <div class="col-md-6 mb-3">
                            {{ form.id_number.label(class="form-label") }}
                            {{ form.id_number(class="form-control", placeholder="مثال: 1234567890 أو 2345678901 أو V123456789") }}
                            {% if form.id_number.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.id_number.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- رقم الجوال -->
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control", placeholder="مثال: 0501234567") }}
                            {% if form.phone.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.phone.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ التوظيف -->
                        <div class="col-md-6 mb-3">
                            {{ form.hire_date.label(class="form-label") }}
                            {{ form.hire_date(class="form-control") }}
                            {% if form.hire_date.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.hire_date.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الراتب الأساسي -->
                        <div class="col-md-6 mb-3">
                            {{ form.basic_salary.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.basic_salary(class="form-control", step="0.01") }}
                                <span class="input-group-text">ر.ق</span>
                            </div>
                            {% if form.basic_salary.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.basic_salary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.status.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('workers') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>

                        <div>
                            {% if worker %}
                                <button type="button" class="btn btn-outline-danger me-2"
                                        onclick="confirmDelete({{ worker.id }}, '{{ worker.name }}')">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف العامل
                                </button>
                            {% endif %}

                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if worker and worker.salaries %}
        <div class="card mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    تاريخ الرواتب
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>الشهر</th>
                                <th>الراتب الأساسي</th>
                                <th>البدلات</th>
                                <th>الخصومات</th>
                                <th>صافي الراتب</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for salary in worker.salaries[-5:] %}
                            <tr>
                                <td>{{ salary.get_month_name() }} {{ salary.year }}</td>
                                <td>{{ "{:,.0f}".format(salary.basic_salary) }}</td>
                                <td>{{ "{:,.0f}".format(salary.allowances) }}</td>
                                <td>{{ "{:,.0f}".format(salary.deductions) }}</td>
                                <td class="fw-bold">{{ "{:,.0f}".format(salary.net_salary) }}</td>
                                <td>
                                    {% if salary.payment_status == 'paid' %}
                                        <span class="badge bg-success">مدفوع</span>
                                    {% else %}
                                        <span class="badge bg-warning">غير مدفوع</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if worker.salaries|length > 5 %}
                    <div class="text-center">
                        <small class="text-muted">عرض آخر 5 رواتب فقط</small>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if worker %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="workerName"></strong>؟</p>
                {% if worker.salaries %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تحذير: هذا العامل لديه {{ worker.salaries|length }} راتب مسجل. لا يمكن حذفه.
                    </div>
                {% else %}
                    <p class="text-danger small">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        لا يمكن التراجع عن هذا الإجراء
                    </p>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                {% if not worker.salaries %}
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if worker %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = '/workers/delete/' + workerId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endif %}
{% endblock %}
