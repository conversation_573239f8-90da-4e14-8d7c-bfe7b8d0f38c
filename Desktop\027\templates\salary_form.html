{% extends "base.html" %}

{% block title %}{{ title }} - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-money-bill-wave me-2"></i>{{ title }}</h2>
            <p class="text-muted mb-0">إدخال وتعديل بيانات الراتب</p>
        </div>
        <a href="{{ url_for('salaries') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    بيانات الراتب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="salaryForm">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <!-- العامل -->
                        <div class="col-md-6 mb-3">
                            {{ form.worker_id.label(class="form-label") }}
                            {{ form.worker_id(class="form-select", onchange="loadWorkerSalary()") }}
                            {% if form.worker_id.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.worker_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الشهر -->
                        <div class="col-md-3 mb-3">
                            {{ form.month.label(class="form-label") }}
                            {{ form.month(class="form-select") }}
                            {% if form.month.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.month.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- السنة -->
                        <div class="col-md-3 mb-3">
                            {{ form.year.label(class="form-label") }}
                            {{ form.year(class="form-control") }}
                            {% if form.year.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.year.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الراتب الأساسي -->
                        <div class="col-md-4 mb-3">
                            {{ form.basic_salary.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.basic_salary(class="form-control", step="0.01", onchange="calculateNet()") }}
                                <span class="input-group-text">ر.ق</span>
                            </div>
                            {% if form.basic_salary.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.basic_salary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- البدلات -->
                        <div class="col-md-4 mb-3">
                            {{ form.allowances.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.allowances(class="form-control", step="0.01", onchange="calculateNet()") }}
                                <span class="input-group-text">ر.ق</span>
                            </div>
                            {% if form.allowances.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.allowances.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الخصومات -->
                        <div class="col-md-4 mb-3">
                            {{ form.deductions.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.deductions(class="form-control", step="0.01", onchange="calculateNet()") }}
                                <span class="input-group-text">ر.ق</span>
                            </div>
                            {% if form.deductions.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.deductions.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- صافي الراتب (محسوب تلقائياً) -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">صافي الراتب</label>
                            <div class="input-group">
                                <input type="text" id="netSalary" class="form-control fw-bold text-success" readonly>
                                <span class="input-group-text">ر.ق</span>
                            </div>
                            <small class="text-muted">يتم حساب صافي الراتب تلقائياً</small>
                        </div>

                        <!-- حالة الدفع -->
                        <div class="col-md-6 mb-3">
                            {{ form.payment_status.label(class="form-label") }}
                            {{ form.payment_status(class="form-select", onchange="togglePaymentDate()") }}
                            {% if form.payment_status.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.payment_status.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- تاريخ الدفع -->
                        <div class="col-md-6 mb-3" id="paymentDateDiv">
                            {{ form.payment_date.label(class="form-label") }}
                            {{ form.payment_date(class="form-control") }}
                            {% if form.payment_date.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.payment_date.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="أي ملاحظات إضافية...") }}
                        {% if form.notes.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.notes.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('salaries') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>

                        <div>
                            {% if salary %}
                                <button type="button" class="btn btn-outline-info me-2"
                                        onclick="printReceipt({{ salary.id }})">
                                    <i class="fas fa-print me-2"></i>
                                    طباعة إيصال
                                </button>

                                <button type="button" class="btn btn-outline-danger me-2"
                                        onclick="confirmDelete({{ salary.id }}, '{{ salary.worker.name }}', '{{ salary.get_month_name() }} {{ salary.year }}')">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف الراتب
                                </button>
                            {% endif %}

                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- آلة الحاسبة -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    آلة حاسبة
                </h5>
            </div>
            <div class="card-body">
                <div class="calculator">
                    <div class="calculator-display">
                        <input type="text" id="calc-display" class="form-control text-end" readonly value="0">
                    </div>
                    <div class="calculator-buttons mt-3">
                        <div class="row g-2">
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-danger w-100" onclick="clearCalc()">C</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-warning w-100" onclick="deleteLast()">⌫</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="appendToCalc('/')">/</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="appendToCalc('*')">×</button>
                            </div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('7')">7</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('8')">8</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('9')">9</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="appendToCalc('-')">-</button>
                            </div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('4')">4</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('5')">5</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('6')">6</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="appendToCalc('+')">+</button>
                            </div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('1')">1</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('2')">2</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('3')">3</button>
                            </div>
                            <div class="col-3 row-span-2">
                                <button type="button" class="btn btn-success w-100 h-100" onclick="calculateResult()" style="height: 76px;">=</button>
                            </div>
                        </div>
                        <div class="row g-2 mt-1">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('0')">0</button>
                            </div>
                            <div class="col-3">
                                <button type="button" class="btn btn-outline-dark w-100" onclick="appendToCalc('.')">.</button>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- أزرار سريعة لنقل النتيجة -->
                <div class="quick-actions">
                    <h6 class="text-muted">نقل النتيجة إلى:</h6>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="transferToField('basic_salary')">
                            <i class="fas fa-arrow-right me-1"></i>
                            الراتب الأساسي
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="transferToField('allowances')">
                            <i class="fas fa-arrow-right me-1"></i>
                            البدلات
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="transferToField('deductions')">
                            <i class="fas fa-arrow-right me-1"></i>
                            الخصومات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات مفيدة -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <p><strong>الراتب الأساسي:</strong> المبلغ الثابت الشهري</p>
                    <p><strong>البدلات:</strong> مثل بدل السكن، المواصلات، الطعام</p>
                    <p><strong>الخصومات:</strong> مثل التأمين، القروض، الغياب</p>
                    <p><strong>صافي الراتب:</strong> الراتب الأساسي + البدلات - الخصومات</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <!-- Calculation Summary -->
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    ملخص الحساب
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <h6 class="text-muted mb-1">الراتب الأساسي</h6>
                        <h5 id="displayBasic" class="text-primary mb-0">0 ر.ق</h5>
                    </div>
                    <div class="col-3">
                        <h6 class="text-muted mb-1">البدلات</h6>
                        <h5 id="displayAllowances" class="text-success mb-0">0 ر.ق</h5>
                    </div>
                    <div class="col-3">
                        <h6 class="text-muted mb-1">الخصومات</h6>
                        <h5 id="displayDeductions" class="text-danger mb-0">0 ر.ق</h5>
                    </div>
                    <div class="col-3">
                        <h6 class="text-muted mb-1">صافي الراتب</h6>
                        <h5 id="displayNet" class="text-dark fw-bold mb-0">0 ر.ق</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if salary %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف راتب <strong id="workerName"></strong> لشهر <strong id="salaryMonth"></strong>؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// حساب صافي الراتب
function calculateNet() {
    const basic = parseFloat(document.getElementById('basic_salary').value) || 0;
    const allowances = parseFloat(document.getElementById('allowances').value) || 0;
    const deductions = parseFloat(document.getElementById('deductions').value) || 0;

    const net = basic + allowances - deductions;

    document.getElementById('netSalary').value = net.toLocaleString('ar-SA', {minimumFractionDigits: 0, maximumFractionDigits: 0});

    // تحديث العرض
    document.getElementById('displayBasic').textContent = basic.toLocaleString('ar-SA') + ' ر.ق';
    document.getElementById('displayAllowances').textContent = allowances.toLocaleString('ar-SA') + ' ر.ق';
    document.getElementById('displayDeductions').textContent = deductions.toLocaleString('ar-SA') + ' ر.ق';
    document.getElementById('displayNet').textContent = net.toLocaleString('ar-SA') + ' ر.ق';
}

// إظهار/إخفاء تاريخ الدفع
function togglePaymentDate() {
    const status = document.getElementById('payment_status').value;
    const paymentDateDiv = document.getElementById('paymentDateDiv');

    if (status === 'paid') {
        paymentDateDiv.style.display = 'block';
        if (!document.getElementById('payment_date').value) {
            document.getElementById('payment_date').value = new Date().toISOString().split('T')[0];
        }
    } else {
        paymentDateDiv.style.display = 'none';
        document.getElementById('payment_date').value = '';
    }
}

// تحميل راتب العامل الأساسي
function loadWorkerSalary() {
    const workerId = document.getElementById('worker_id').value;
    if (workerId) {
        fetch('/api/worker/' + workerId + '/basic_salary')
            .then(response => response.json())
            .then(data => {
                if (data.basic_salary) {
                    document.getElementById('basic_salary').value = data.basic_salary;
                    calculateNet();
                }
            })
            .catch(error => console.error('Error:', error));
    }
}

// تأكيد الحذف
{% if salary %}
function confirmDelete(salaryId, workerName, salaryMonth) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('salaryMonth').textContent = salaryMonth;
    document.getElementById('deleteForm').action = '/salaries/delete/' + salaryId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function printReceipt(salaryId) {
    window.open('/salary/receipt/' + salaryId, '_blank');
}
{% endif %}

// ==================== آلة الحاسبة ====================
let calcDisplay = '';
let calcResult = 0;

function appendToCalc(value) {
    const display = document.getElementById('calc-display');
    if (calcDisplay === '0' || calcDisplay === '') {
        calcDisplay = value;
    } else {
        calcDisplay += value;
    }
    display.value = calcDisplay;
}

function clearCalc() {
    calcDisplay = '0';
    calcResult = 0;
    document.getElementById('calc-display').value = '0';
}

function deleteLast() {
    if (calcDisplay.length > 1) {
        calcDisplay = calcDisplay.slice(0, -1);
    } else {
        calcDisplay = '0';
    }
    document.getElementById('calc-display').value = calcDisplay;
}

function calculateResult() {
    try {
        // استبدال × بـ *
        let expression = calcDisplay.replace(/×/g, '*');
        calcResult = eval(expression);
        calcDisplay = calcResult.toString();
        document.getElementById('calc-display').value = calcResult;
    } catch (error) {
        document.getElementById('calc-display').value = 'خطأ';
        calcDisplay = '0';
    }
}

function transferToField(fieldName) {
    const result = document.getElementById('calc-display').value;
    if (result && result !== 'خطأ' && result !== '0') {
        document.getElementById(fieldName).value = parseFloat(result);
        calculateNet(); // إعادة حساب صافي الراتب

        // إظهار رسالة تأكيد
        const fieldLabels = {
            'basic_salary': 'الراتب الأساسي',
            'allowances': 'البدلات',
            'deductions': 'الخصومات'
        };

        // إضافة تأثير بصري
        const field = document.getElementById(fieldName);
        field.style.backgroundColor = '#d4edda';
        setTimeout(() => {
            field.style.backgroundColor = '';
        }, 1000);
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateNet();
    togglePaymentDate();
    clearCalc(); // تهيئة آلة الحاسبة
});
</script>
{% endblock %}
