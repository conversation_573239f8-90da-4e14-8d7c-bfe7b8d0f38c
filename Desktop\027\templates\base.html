<!DOCTYPE html>
<html lang="{{ get_lang() }}" dir="{{ 'rtl' if is_rtl() else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ _('home_payroll_system') }}{% endblock %}</title>

    <!-- Bootstrap CSS (RTL/LTR) -->
    {% if is_rtl() %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn {
            border-radius: 8px;
            font-weight: 600;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* تنسيق الشريط المتحرك */
        .ticker-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 0;
            overflow: hidden;
            white-space: nowrap;
            position: relative;
            z-index: 1000;
            border-bottom: 2px solid #5a67d8;
        }

        .ticker-content {
            display: inline-block;
            animation: scroll-rtl 30s linear infinite;
            font-size: 14px;
            font-weight: 500;
            direction: rtl;
        }

        @keyframes scroll-rtl {
            0% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        .ticker-content.slow {
            animation-duration: 60s;
        }

        .ticker-content.normal {
            animation-duration: 30s;
        }

        .ticker-content.fast {
            animation-duration: 15s;
        }

        #ticker-text {
            padding-right: 50px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <!-- الشريط المتحرك -->
    <div id="ticker-container" class="ticker-container" style="display: none;">
        <div class="ticker-content">
            <span id="ticker-text"></span>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-money-check-alt"></i>
                        {{ _('payroll_system') }}
                    </h4>

                    <nav class="nav flex-column">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            {{ _('dashboard') }}
                        </a>
                        <a class="nav-link {% if request.endpoint == 'workers' %}active{% endif %}" href="{{ url_for('workers') }}">
                            <i class="fas fa-users me-2"></i>
                            {{ _('manage_workers') }}
                        </a>
                        <a class="nav-link {% if request.endpoint == 'salaries' %}active{% endif %}" href="{{ url_for('salaries') }}">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            {{ _('manage_salaries') }}
                        </a>
                        <a class="nav-link {% if request.endpoint == 'reports' %}active{% endif %}" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar me-2"></i>
                            {{ _('view_reports') }}
                        </a>

                        {% if session.get('user_role') == 'admin' %}
                        <hr class="my-3">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>{{ _('system_management') }}</span>
                        </h6>
                        <a class="nav-link {% if request.endpoint in ['users', 'add_user', 'edit_user'] %}active{% endif %}" href="{{ url_for('users') }}">
                            <i class="fas fa-users-cog me-2"></i>
                            {{ _('user_management') }}
                        </a>
                        <a class="nav-link {% if request.endpoint in ['settings', 'system_settings', 'report_settings', 'print_settings'] %}active{% endif %}" href="{{ url_for('settings') }}">
                            <i class="fas fa-cogs me-2"></i>
                            {{ _('system_settings') }}
                        </a>
                        {% endif %}

                        <hr class="my-3">

                        <!-- تغيير اللغة -->
                        <div class="dropdown mb-2">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-globe me-2"></i>
                                {{ _('language') }}: {% if get_lang() == 'ar' %}{{ _('arabic') }}{% else %}{{ _('english') }}{% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('change_language', lang='ar') }}">
                                    <i class="fas fa-flag me-2"></i>{{ _('arabic') }} 🇶🇦
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('change_language', lang='en') }}">
                                    <i class="fas fa-flag me-2"></i>{{ _('english') }} 🇺🇸
                                </a></li>
                            </ul>
                        </div>

                        <a class="nav-link {% if request.endpoint == 'profile' %}active{% endif %}" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-2"></i>
                            {{ _('profile') }}
                        </a>
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            {{ _('logout') }}
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    {% else %}
        {% block login_content %}{% endblock %}
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript للشريط المتحرك -->
    <script>
        // تحميل وإعداد الشريط المتحرك
        function loadTicker() {
            fetch('/api/ticker')
                .then(response => response.json())
                .then(data => {
                    if (data.enabled) {
                        const tickerContainer = document.getElementById('ticker-container');
                        const tickerText = document.getElementById('ticker-text');
                        const tickerContent = document.querySelector('.ticker-content');

                        // تعيين المحتوى
                        tickerText.textContent = data.content;

                        // تعيين السرعة
                        tickerContent.className = 'ticker-content ' + getSpeedClass(data.speed);

                        // إظهار الشريط المتحرك
                        tickerContainer.style.display = 'block';
                    } else {
                        // إخفاء الشريط المتحرك
                        document.getElementById('ticker-container').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل الشريط المتحرك:', error);
                    document.getElementById('ticker-container').style.display = 'none';
                });
        }

        // تحديد فئة السرعة
        function getSpeedClass(speed) {
            if (speed <= 60) return 'slow';
            if (speed >= 100) return 'fast';
            return 'normal';
        }

        // تحميل الشريط المتحرك عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadTicker();

            // تحديث الشريط المتحرك كل 30 ثانية
            setInterval(loadTicker, 30000);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
