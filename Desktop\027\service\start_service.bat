@echo off
chcp 65001 >nul
title خدمة نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🌐 خدمة نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 🖥️ تشغيل كخدمة ويب على الشبكة المحلية
echo ============================================================

echo 🔍 البحث عن الملف التنفيذي...
if exist "../dist/PayrollSystem.exe" (
    echo ✅ تم العثور على الملف التنفيذي

    echo 🌐 الحصول على عنوان IP...
    for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
        set "IP=%%a"
        goto :found
    )
    :found
    set IP=%IP: =%

    echo ============================================================
    echo 🚀 بدء تشغيل الخدمة...
    echo 🌐 الخدمة متاحة على:
    echo    - محلي: http://localhost:5000
    echo    - الشبكة: http://%IP%:5000
    echo 🔑 بيانات الدخول: admin / admin123
    echo ============================================================
    echo ⚠️ لا تغلق هذه النافذة - الخدمة تعمل
    echo 🛑 لإيقاف الخدمة: اضغط Ctrl+C أو أغلق النافذة
    echo ============================================================

    cd ../dist
    PayrollSystem.exe

) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 🏗️ يرجى بناء الملف التنفيذي أولاً
    pause
)
