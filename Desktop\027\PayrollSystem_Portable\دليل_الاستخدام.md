# 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
## 📱 تطبيق مستقل - يعمل بدون إنترنت

---

## 🎯 **نظرة عامة:**

هذا نظام شامل لإدارة رواتب العمالة المنزلية في دولة قطر، مصمم ليعمل بدون إنترنت على أي كمبيوتر.

### ✨ **المميزات الرئيسية:**
- 🌐 **يعمل بدون إنترنت** - لا يحتاج اتصال بالإنترنت
- 💻 **تطبيق مستقل** - يمكن تثبيته على أي كمبيوتر
- 🇶🇦 **مخصص لدولة قطر** - يدعم الريال القطري والقوانين المحلية
- 🌍 **متعدد اللغات** - عربي وإنجليزي مع تبديل فوري
- 📄 **تقارير احترافية** - PDF وExcel بالاتجاه الصحيح
- 🖨️ **طباعة محسنة** - إيصالات A5 مثالية للطباعة

---

## 🚀 **طرق التشغيل:**

### 📦 **الطريقة الأولى: تشغيل مباشر (يتطلب Python)**

#### 1️⃣ **تثبيت المتطلبات:**
```bash
# تشغيل ملف التثبيت
install_requirements.bat
```

#### 2️⃣ **تشغيل النظام:**
```bash
# تشغيل النظام
start_system.bat
```

### 🏗️ **الطريقة الثانية: بناء ملف exe مستقل**

#### 1️⃣ **بناء الملف التنفيذي:**
```bash
# بناء ملف exe
build_exe.bat
```

#### 2️⃣ **تشغيل الملف التنفيذي:**
- انتقل إلى مجلد `dist`
- شغل ملف `PayrollSystem.exe`

---

## 📋 **متطلبات النظام:**

### 💻 **الحد الأدنى:**
- **نظام التشغيل:** Windows 7/8/10/11
- **المعالج:** Intel/AMD 1 GHz
- **الذاكرة:** 2 GB RAM
- **التخزين:** 500 MB مساحة فارغة
- **الشاشة:** 1024x768 دقة

### 🎯 **الموصى به:**
- **نظام التشغيل:** Windows 10/11
- **المعالج:** Intel/AMD 2 GHz أو أعلى
- **الذاكرة:** 4 GB RAM أو أكثر
- **التخزين:** 1 GB مساحة فارغة
- **الشاشة:** 1366x768 دقة أو أعلى

---

## 🔧 **التثبيت والإعداد:**

### 📦 **تثبيت Python (للطريقة الأولى):**
1. حمل Python من: https://python.org
2. تأكد من تحديد "Add Python to PATH"
3. شغل `install_requirements.bat`

### 🏗️ **بناء ملف exe (للطريقة الثانية):**
1. تأكد من تثبيت Python والمكتبات
2. شغل `build_exe.bat`
3. انتظر حتى انتهاء البناء
4. ستجد الملف في مجلد `dist`

---

## 🌐 **الوصول للنظام:**

### 🔗 **الرابط المحلي:**
```
http://localhost:5000
```

### 🔑 **بيانات الدخول الافتراضية:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### 📱 **الوصول من أجهزة أخرى:**
```
http://[عنوان-IP-للكمبيوتر]:5000
```

---

## 📊 **المميزات الشاملة:**

### 👥 **إدارة العمال:**
- ✅ **200+ جنسية** من جميع أنحاء العالم
- ✅ **35+ نوع عمل** متنوعة ومصنفة
- ✅ **بيانات شاملة** (جواز، إقامة، هاتف)
- ✅ **تصنيف حسب الجنسية** ونوع العمل

### 💰 **إدارة الرواتب:**
- ✅ **آلة حاسبة متكاملة** في صفحة الرواتب
- ✅ **حساب تلقائي** للصافي والخصومات
- ✅ **تتبع حالة الدفع** (مدفوع/غير مدفوع)
- ✅ **تواريخ الدفع** والملاحظات

### 📄 **التقارير والطباعة:**
- ✅ **تقارير PDF متعددة اللغات** مع اتجاه صحيح
- ✅ **تقارير Excel محسنة** بالعملة القطرية
- ✅ **إيصالات راتب A5** مثالية للطباعة
- ✅ **طباعة عربية محسنة** مع معالجة النصوص

### 🔐 **إدارة المستخدمين:**
- ✅ **نظام مستخدمين متقدم** مع أدوار وصلاحيات
- ✅ **مدير النظام** و **مستخدم عادي**
- ✅ **تغيير كلمات المرور** والملفات الشخصية
- ✅ **أمان متقدم** للبيانات

### ⚙️ **الإعدادات والتخصيص:**
- ✅ **نظام إعدادات شامل** لتخصيص كامل للنظام
- ✅ **إعدادات الشركة** (الاسم، العنوان، الهاتف)
- ✅ **إعدادات العملة** والمنطقة الزمنية
- ✅ **إعدادات التقارير** والطباعة

### 🌐 **متعدد اللغات:**
- ✅ **نظام متعدد اللغات محسن** (عربي/إنجليزي)
- ✅ **تبديل فوري** بين اللغات
- ✅ **اتجاه صحيح** لكل لغة (RTL/LTR)
- ✅ **ترجمة شاملة** لجميع العناصر

### 🎨 **الواجهة والتصميم:**
- ✅ **واجهة عربية متجاوبة** ومتكاملة
- ✅ **تصميم احترافي** بألوان متناسقة
- ✅ **شريط متحرك تفاعلي** قابل للتخصيص
- ✅ **أيقونات واضحة** ومفهومة

---

## 🗂️ **هيكل الملفات:**

```
📁 نظام الرواتب/
├── 📄 run.py                    # ملف التشغيل الرئيسي
├── 📄 run_standalone.py         # ملف التشغيل المستقل
├── 📄 app.py                    # التطبيق الرئيسي
├── 📄 models.py                 # نماذج قاعدة البيانات
├── 📄 forms.py                  # نماذج الإدخال
├── 📄 config.py                 # إعدادات التطبيق
├── 📄 translations.py           # ملف الترجمات
├── 📄 requirements.txt          # المكتبات المطلوبة
├── 📄 payroll_system.spec       # ملف PyInstaller
├── 📄 build_exe.bat            # بناء ملف exe
├── 📄 install_requirements.bat  # تثبيت المتطلبات
├── 📄 start_system.bat         # تشغيل النظام
├── 📁 templates/               # قوالب HTML
├── 📁 static/                  # ملفات CSS/JS
├── 📁 fonts/                   # الخطوط العربية
└── 📁 dist/                    # الملف التنفيذي (بعد البناء)
    └── 📄 PayrollSystem.exe
```

---

## 🛠️ **استكشاف الأخطاء:**

### ❌ **مشاكل شائعة وحلولها:**

#### 🐍 **Python غير مثبت:**
```
الحل: حمل وثبت Python من python.org
```

#### 📦 **مكتبات مفقودة:**
```
الحل: شغل install_requirements.bat
```

#### 🔒 **مشكلة في الصلاحيات:**
```
الحل: شغل كمدير (Run as Administrator)
```

#### 🌐 **لا يفتح المتصفح:**
```
الحل: افتح المتصفح يدوياً واذهب إلى localhost:5000
```

#### 💾 **مشكلة في قاعدة البيانات:**
```
الحل: احذف ملف payroll.db وأعد تشغيل النظام
```

---

## 📞 **الدعم والمساعدة:**

### 🆘 **في حالة وجود مشاكل:**
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من تثبيت جميع المكتبات
3. تأكد من وجود صلاحيات الكتابة في المجلد
4. جرب تشغيل النظام كمدير
5. تأكد من عدم استخدام برامج مكافحة الفيروسات لحجب التطبيق

### 📋 **معلومات النظام:**
- **الإصدار:** 12.0 - Standalone Edition
- **تاريخ الإصدار:** 29 مايو 2025
- **متوافق مع:** Windows 7/8/10/11
- **اللغات:** العربية والإنجليزية

---

## 🎉 **مبروك!**

تم تجهيز نظام صرف رواتب العمالة المنزلية بنجاح!

النظام الآن:
- 💻 **يعمل بدون إنترنت** على أي كمبيوتر
- 🏗️ **يمكن تحويله لملف exe** مستقل
- 🇶🇦 **مخصص لدولة قطر** بالكامل
- 🌐 **متعدد اللغات** مع تبديل فوري
- 📊 **شامل جميع المميزات** المطلوبة

**استمتع باستخدام النظام!** 🚀
