{% extends "base.html" %}

{% block title %}{{ title }} - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-user-edit me-2"></i>{{ title }}</h2>
            <p class="text-muted mb-0">إدخال وتعديل بيانات المستخدم</p>
        </div>
        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.username.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">يستخدم لتسجيل الدخول</small>
                        </div>
                        
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control") }}
                            {% if form.full_name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.full_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if user %}
                                <small class="text-muted">اتركها فارغة إذا كنت لا تريد تغييرها</small>
                            {% else %}
                                <small class="text-muted">مطلوبة للمستخدم الجديد</small>
                            {% endif %}
                        </div>
                        
                        <!-- تأكيد كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control") }}
                            {% if form.confirm_password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.confirm_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الدور -->
                        <div class="col-md-6 mb-3">
                            {{ form.role.label(class="form-label") }}
                            {{ form.role(class="form-select") }}
                            {% if form.role.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.role.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">
                                <strong>مدير النظام:</strong> صلاحيات كاملة<br>
                                <strong>مدير:</strong> إدارة العمال والرواتب<br>
                                <strong>موظف:</strong> إضافة وتعديل البيانات<br>
                                <strong>مستعرض:</strong> عرض البيانات فقط
                            </small>
                        </div>
                        
                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            {{ form.is_active.label(class="form-label") }}
                            {{ form.is_active(class="form-select") }}
                            {% if form.is_active.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.is_active.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">المستخدمون غير النشطين لا يمكنهم تسجيل الدخول</small>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        
                        <div>
                            {% if user and user.id != session.user_id %}
                                <button type="button" class="btn btn-outline-danger me-2" 
                                        onclick="confirmDelete({{ user.id }}, '{{ user.username }}')">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف المستخدم
                                </button>
                            {% endif %}
                            
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        {% if user %}
        <div class="card mt-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ user.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                        <p><strong>آخر تحديث:</strong> {{ user.created_at.strftime('%Y/%m/%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>حالة الحساب:</strong> 
                            {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                        <p><strong>الدور:</strong> 
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger">مدير النظام</span>
                            {% elif user.role == 'manager' %}
                                <span class="badge bg-warning">مدير</span>
                            {% elif user.role == 'employee' %}
                                <span class="badge bg-primary">موظف</span>
                            {% else %}
                                <span class="badge bg-secondary">مستعرض فقط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if user and user.id != session.user_id %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم <strong id="userName"></strong>؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if user and user.id != session.user_id %}
<script>
function confirmDelete(userId, userName) {
    document.getElementById('userName').textContent = userName;
    document.getElementById('deleteForm').action = '/users/delete/' + userId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endif %}
{% endblock %}
