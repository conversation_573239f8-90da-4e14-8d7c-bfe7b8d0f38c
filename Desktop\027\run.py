#!/usr/bin/env python3
"""
ملف تشغيل نظام صرف رواتب العمالة المنزلية
"""

from app import app, db, create_default_user

if __name__ == '__main__':
    with app.app_context():
        # إنشاء قاعدة البيانات والجداول
        db.create_all()
        
        # إنشاء المستخدم الافتراضي
        create_default_user()
        
        print("=" * 60)
        print("🏠 نظام صرف رواتب العمالة المنزلية")
        print("=" * 60)
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        print("✅ تم إنشاء المستخدم الافتراضي:")
        print("   👤 اسم المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("=" * 60)
        print("🌐 الخادم يعمل على: http://localhost:5000")
        print("📱 للوصول من أجهزة أخرى: http://[IP-ADDRESS]:5000")
        print("=" * 60)
        print("🛑 لإيقاف الخادم: اضغط Ctrl+C")
        print("=" * 60)
    
    # تشغيل التطبيق
    app.run(
        debug=True,
        host='0.0.0.0',  # للسماح بالوصول من أجهزة أخرى في الشبكة
        port=5000,
        threaded=True
    )
