{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-cogs me-2"></i>إعدادات النظام</h2>
            <p class="text-muted mb-0">إدارة وتخصيص إعدادات النظام والتقارير والطباعة</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- إعدادات النظام -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-desktop me-2"></i>
                    إعدادات النظام
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إعدادات الشركة والعملة واللغة والمنطقة الزمنية</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>معلومات الشركة</li>
                    <li><i class="fas fa-check text-success me-2"></i>إعدادات العملة</li>
                    <li><i class="fas fa-check text-success me-2"></i>لغة النظام</li>
                    <li><i class="fas fa-check text-success me-2"></i>المنطقة الزمنية</li>
                    <li><i class="fas fa-check text-success me-2"></i>تنسيق التاريخ</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('system_settings') }}" class="btn btn-primary w-100">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الإعدادات
                </a>
            </div>
        </div>
    </div>

    <!-- إعدادات التقارير -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-pdf me-2"></i>
                    إعدادات التقارير
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">تخصيص شكل ومحتوى التقارير وملفات PDF</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>حجم واتجاه الصفحة</li>
                    <li><i class="fas fa-check text-success me-2"></i>حجم الخط</li>
                    <li><i class="fas fa-check text-success me-2"></i>شعار الشركة</li>
                    <li><i class="fas fa-check text-success me-2"></i>ملخص الإحصائيات</li>
                    <li><i class="fas fa-check text-success me-2"></i>ألوان التقرير</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('report_settings') }}" class="btn btn-success w-100">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الإعدادات
                </a>
            </div>
        </div>
    </div>

    <!-- إعدادات الطباعة -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-print me-2"></i>
                    إعدادات الطباعة
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إعدادات الطابعة وجودة الطباعة والهوامش</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>الطابعة الافتراضية</li>
                    <li><i class="fas fa-check text-success me-2"></i>عدد النسخ</li>
                    <li><i class="fas fa-check text-success me-2"></i>حجم الورق</li>
                    <li><i class="fas fa-check text-success me-2"></i>جودة الطباعة</li>
                    <li><i class="fas fa-check text-success me-2"></i>الهوامش</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('print_settings') }}" class="btn btn-info w-100">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الإعدادات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أدوات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    أدوات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-undo me-2"></i>إعادة تعيين الإعدادات</h6>
                        <p class="text-muted">إعادة جميع الإعدادات للقيم الافتراضية</p>
                        <button type="button" class="btn btn-outline-warning" onclick="confirmReset('all')">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين جميع الإعدادات
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-download me-2"></i>نسخ احتياطي</h6>
                        <p class="text-muted">تصدير الإعدادات الحالية كنسخة احتياطية</p>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportSettings()">
                            <i class="fas fa-download me-2"></i>
                            تصدير الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h6 class="text-muted">اسم النظام</h6>
                        <p class="mb-0">نظام صرف رواتب العمالة المنزلية</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">الإصدار</h6>
                        <p class="mb-0">7.0 - Complete Settings Edition</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">الدولة</h6>
                        <p class="mb-0">دولة قطر 🇶🇦</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">العملة</h6>
                        <p class="mb-0">الريال القطري (ر.ق)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين الإعدادات للقيم الافتراضية؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم فقدان جميع الإعدادات المخصصة
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="resetForm" method="POST" action="{{ url_for('reset_settings') }}" style="display: inline;">
                    <input type="hidden" name="category" id="resetCategory" value="all">
                    <button type="submit" class="btn btn-warning">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReset(category) {
    document.getElementById('resetCategory').value = category;
    new bootstrap.Modal(document.getElementById('resetModal')).show();
}

function exportSettings() {
    // يمكن إضافة وظيفة تصدير الإعدادات لاحقاً
    alert('ميزة تصدير الإعدادات ستكون متاحة قريباً');
}
</script>
{% endblock %}
