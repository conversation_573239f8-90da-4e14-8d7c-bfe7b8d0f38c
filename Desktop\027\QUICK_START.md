# دليل البدء السريع - نظام صرف رواتب العمالة المنزلية

## 🚀 تشغيل النظام

### الطريقة الأولى (الموصى بها):
```bash
python run.py
```

### الطريقة الثانية:
```bash
python app.py
```

## 🌐 الوصول للنظام

افتح المتصفح وانتقل إلى: **http://localhost:5000**

## 🔑 بيانات الدخول

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 📋 خطوات الاستخدام السريع

### 1. إضافة عامل جديد
1. اضغط على "إدارة العمال" من القائمة الجانبية
2. اضغط "إضافة عامل جديد"
3. املأ البيانات:
   - الاسم الكامل
   - الجنسية
   - نوع العمل
   - رقم الهوية
   - رقم الجوال (اختياري)
   - تاريخ التوظيف
   - الراتب الأساسي
4. اضغط "حفظ"

### 2. إضافة راتب شهري
1. اضغط على "إدارة الرواتب" من القائمة الجانبية
2. اضغط "إضافة راتب جديد"
3. اختر العامل من القائمة
4. اختر الشهر والسنة
5. أدخل:
   - الراتب الأساسي (سيتم تعبئته تلقائياً)
   - البدلات (إن وجدت)
   - الخصومات (إن وجدت)
6. اختر حالة الدفع
7. اضغط "حفظ"

### 3. طباعة إيصال راتب
1. من صفحة "إدارة الرواتب"
2. اضغط على أيقونة الطباعة 🖨️ بجانب الراتب المطلوب
3. سيتم فتح إيصال PDF جاهز للطباعة

### 4. إنشاء تقرير
1. اضغط على "التقارير" من القائمة الجانبية
2. اختر معايير التقرير:
   - الشهر (أو جميع الشهور)
   - السنة
   - حالة الدفع
3. اضغط "معاينة التقرير" لعرض البيانات
4. اضغط "تصدير PDF" أو "تصدير Excel" لتحميل التقرير

## 🔧 نصائح مهمة

### ✅ ما يجب فعله:
- قم بتغيير كلمة المرور الافتراضية
- أدخل بيانات العمال بدقة
- تأكد من صحة أرقام الهوية (لا يمكن تكرارها)
- راجع الرواتب قبل وضع علامة "مدفوع"

### ❌ ما يجب تجنبه:
- لا تحذف عامل له رواتب مسجلة
- لا تضع راتبين لنفس العامل في نفس الشهر
- لا تترك الحقول المطلوبة فارغة

## 🛠️ حل المشاكل الشائعة

### المشكلة: لا يمكن الوصول للموقع
**الحل:** تأكد من تشغيل الخادم بالأمر `python run.py`

### المشكلة: خطأ في قاعدة البيانات
**الحل:** احذف ملف `instance/payroll.db` وأعد تشغيل النظام

### المشكلة: لا يمكن إضافة عامل
**الحل:** تأكد من أن رقم الهوية غير مكرر

### المشكلة: لا يمكن إضافة راتب
**الحل:** تأكد من عدم وجود راتب لنفس العامل في نفس الشهر

## 📱 الوصول من أجهزة أخرى

إذا كنت تريد الوصول للنظام من أجهزة أخرى في نفس الشبكة:

1. اعرف عنوان IP الخاص بجهازك
2. استخدم العنوان: `http://[IP-ADDRESS]:5000`

مثال: `http://*************:5000`

## 🔒 الأمان

- النظام مصمم للاستخدام المحلي أو في الشبكات الداخلية
- لا تستخدمه على الإنترنت بدون إعدادات أمان إضافية
- قم بعمل نسخ احتياطية من قاعدة البيانات بانتظام

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع رسائل الخطأ في وحدة التحكم
2. تأكد من تثبيت جميع المكتبات: `pip install -r requirements.txt`
3. راجع ملف README.md للمزيد من التفاصيل

---

**نصيحة:** احفظ هذا الملف كمرجع سريع! 📌
