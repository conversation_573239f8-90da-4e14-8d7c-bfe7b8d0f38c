@echo off
chcp 65001 >nul
title إلغاء تثبيت نظام صرف رواتب العمالة المنزلية

echo ============================================================
echo 🗑️ إلغاء تثبيت نظام صرف رواتب العمالة المنزلية
echo ============================================================

set "INSTALL_DIR=%USERPROFILE%\Desktop\نظام الرواتب"

echo ❓ هل تريد حذف النظام نهائياً؟ (Y/N)
set /p confirm=

if /i "%confirm%"=="Y" (
    echo 🗑️ حذف ملفات النظام...
    if exist "%INSTALL_DIR%" rd /s /q "%INSTALL_DIR%"
    
    echo 🔗 حذف الاختصار...
    if exist "%USERPROFILE%\Desktop\نظام الرواتب.lnk" del "%USERPROFILE%\Desktop\نظام الرواتب.lnk"
    
    echo ✅ تم حذف النظام بنجاح!
) else (
    echo ❌ تم إلغاء عملية الحذف
)

echo ============================================================
pause
