{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام صرف رواتب العمالة المنزلية - دولة قطر{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-users-cog me-2"></i>إدارة المستخدمين</h2>
            <p class="text-muted mb-0">إضافة وتعديل وإدارة مستخدمي النظام</p>
        </div>
        <a href="{{ url_for('add_user') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="البحث باسم المستخدم أو الاسم الكامل"
                       value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">الدور</label>
                <select name="role" class="form-select">
                    <option value="all" {% if role_filter == 'all' %}selected{% endif %}>جميع الأدوار</option>
                    <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>مدير النظام</option>
                    <option value="manager" {% if role_filter == 'manager' %}selected{% endif %}>مدير</option>
                    <option value="employee" {% if role_filter == 'employee' %}selected{% endif %}>موظف</option>
                    <option value="viewer" {% if role_filter == 'viewer' %}selected{% endif %}>مستعرض فقط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المستخدمين
            {% if users.total %}
                <span class="badge bg-primary">{{ users.total }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if users.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر نشاط</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr>
                            <td>
                                <strong>{{ user.username }}</strong>
                                {% if user.id == session.user_id %}
                                    <span class="badge bg-info ms-2">أنت</span>
                                {% endif %}
                            </td>
                            <td>{{ user.full_name }}</td>
                            <td>
                                {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">مدير النظام</span>
                                {% elif user.role == 'manager' %}
                                    <span class="badge bg-warning">مدير</span>
                                {% elif user.role == 'employee' %}
                                    <span class="badge bg-primary">موظف</span>
                                {% else %}
                                    <span class="badge bg-secondary">مستعرض فقط</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y/%m/%d') }}</td>
                            <td>
                                {% if user.id == session.user_id %}
                                    <span class="text-success">متصل الآن</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('edit_user', id=user.id) }}" 
                                       class="btn btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if user.id != session.user_id %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="confirmDelete({{ user.id }}, '{{ user.username }}')" 
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if users.pages > 1 %}
            <div class="card-footer bg-white">
                <nav aria-label="صفحات المستخدمين">
                    <ul class="pagination justify-content-center mb-0">
                        {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users', page=users.prev_num, search=search, role=role_filter) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('users', page=page_num, search=search, role=role_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users', page=users.next_num, search=search, role=role_filter) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مستخدمين</h5>
                <p class="text-muted">لم يتم العثور على أي مستخدمين مطابقين لمعايير البحث</p>
                <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة أول مستخدم
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم <strong id="userName"></strong>؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(userId, userName) {
    document.getElementById('userName').textContent = userName;
    document.getElementById('deleteForm').action = '/users/delete/' + userId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
