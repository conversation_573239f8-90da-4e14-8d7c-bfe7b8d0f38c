{% extends "base.html" %}

{% block title %}إدارة الرواتب - نظام صرف رواتب العمالة المنزلية{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h2><i class="fas fa-money-bill-wave me-2"></i>إدارة الرواتب</h2>
            <p class="text-muted mb-0">إضافة وتعديل وإدارة رواتب العمالة المنزلية</p>
        </div>
        <a href="{{ url_for('add_salary') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة راتب جديد
        </a>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">الشهر</label>
                <select name="month" class="form-select">
                    <option value="0" {% if month_filter == 0 %}selected{% endif %}>جميع الشهور</option>
                    <option value="1" {% if month_filter == 1 %}selected{% endif %}>يناير</option>
                    <option value="2" {% if month_filter == 2 %}selected{% endif %}>فبراير</option>
                    <option value="3" {% if month_filter == 3 %}selected{% endif %}>مارس</option>
                    <option value="4" {% if month_filter == 4 %}selected{% endif %}>أبريل</option>
                    <option value="5" {% if month_filter == 5 %}selected{% endif %}>مايو</option>
                    <option value="6" {% if month_filter == 6 %}selected{% endif %}>يونيو</option>
                    <option value="7" {% if month_filter == 7 %}selected{% endif %}>يوليو</option>
                    <option value="8" {% if month_filter == 8 %}selected{% endif %}>أغسطس</option>
                    <option value="9" {% if month_filter == 9 %}selected{% endif %}>سبتمبر</option>
                    <option value="10" {% if month_filter == 10 %}selected{% endif %}>أكتوبر</option>
                    <option value="11" {% if month_filter == 11 %}selected{% endif %}>نوفمبر</option>
                    <option value="12" {% if month_filter == 12 %}selected{% endif %}>ديسمبر</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">السنة</label>
                <input type="number" name="year" class="form-control"
                       value="{{ year_filter }}" min="2020" max="2030">
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة الدفع</label>
                <select name="status" class="form-select">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                    <option value="unpaid" {% if status_filter == 'unpaid' %}selected{% endif %}>غير مدفوع</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-filter me-1"></i>
                        تصفية
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('salaries') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Salaries Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الرواتب
            {% if salaries.total %}
                <span class="badge bg-primary">{{ salaries.total }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if salaries.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>العامل</th>
                            <th>الشهر/السنة</th>
                            <th>الراتب الأساسي</th>
                            <th>البدلات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>حالة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for salary in salaries.items %}
                        <tr>
                            <td>
                                <strong>{{ salary.worker.name }}</strong>
                                <br>
                                <small class="text-muted">{{ salary.worker.job_type }}</small>
                            </td>
                            <td>
                                <strong>{{ salary.get_month_name() }} {{ salary.year }}</strong>
                            </td>
                            <td>{{ "{:,.0f}".format(salary.basic_salary) }} ر.ق</td>
                            <td>{{ "{:,.0f}".format(salary.allowances) }} ر.ق</td>
                            <td>{{ "{:,.0f}".format(salary.deductions) }} ر.ق</td>
                            <td>
                                <span class="fw-bold text-success">
                                    {{ "{:,.0f}".format(salary.net_salary) }} ر.ق
                                </span>
                            </td>
                            <td>
                                {% if salary.payment_status == 'paid' %}
                                    <span class="badge bg-success">مدفوع</span>
                                {% else %}
                                    <span class="badge bg-warning">غير مدفوع</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if salary.payment_date %}
                                    {{ salary.payment_date.strftime('%Y/%m/%d') }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('edit_salary', id=salary.id) }}"
                                       class="btn btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info"
                                            onclick="printReceipt({{ salary.id }})" title="طباعة إيصال">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="confirmDelete({{ salary.id }}, '{{ salary.worker.name }}', '{{ salary.get_month_name() }} {{ salary.year }}')"
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Summary -->
            <div class="card-footer bg-light">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">إجمالي الرواتب</h6>
                        <h5 class="text-primary mb-0">
                            {{ "{:,.0f}".format(salaries.items | sum(attribute='net_salary')) }} ر.ق
                        </h5>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">المدفوع</h6>
                        <h5 class="text-success mb-0">
                            {{ "{:,.0f}".format(salaries.items | selectattr('payment_status', 'equalto', 'paid') | sum(attribute='net_salary')) }} ر.ق
                        </h5>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">غير المدفوع</h6>
                        <h5 class="text-danger mb-0">
                            {{ "{:,.0f}".format(salaries.items | selectattr('payment_status', 'equalto', 'unpaid') | sum(attribute='net_salary')) }} ر.ق
                        </h5>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">عدد الرواتب</h6>
                        <h5 class="text-info mb-0">{{ salaries.items | length }}</h5>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            {% if salaries.pages > 1 %}
            <div class="card-footer bg-white">
                <nav aria-label="صفحات الرواتب">
                    <ul class="pagination justify-content-center mb-0">
                        {% if salaries.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('salaries', page=salaries.prev_num, month=month_filter, year=year_filter, status=status_filter) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for page_num in salaries.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != salaries.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('salaries', page=page_num, month=month_filter, year=year_filter, status=status_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if salaries.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('salaries', page=salaries.next_num, month=month_filter, year=year_filter, status=status_filter) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد رواتب</h5>
                <p class="text-muted">لم يتم العثور على أي رواتب مطابقة لمعايير البحث</p>
                <a href="{{ url_for('add_salary') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة أول راتب
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف راتب <strong id="workerName"></strong> لشهر <strong id="salaryMonth"></strong>؟</p>
                <p class="text-danger small">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لا يمكن التراجع عن هذا الإجراء
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(salaryId, workerName, salaryMonth) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('salaryMonth').textContent = salaryMonth;
    document.getElementById('deleteForm').action = '/salaries/delete/' + salaryId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function printReceipt(salaryId) {
    window.open('/salary/receipt/' + salaryId, '_blank');
}
</script>
{% endblock %}
