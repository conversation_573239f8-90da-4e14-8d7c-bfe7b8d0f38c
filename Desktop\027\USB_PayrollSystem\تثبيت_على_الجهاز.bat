@echo off
chcp 65001 >nul
title تثبيت النظام من USB إلى الجهاز

echo ============================================================
echo 📦 تثبيت نظام الرواتب من USB إلى الجهاز
echo ============================================================

echo ❓ هل تريد تثبيت النظام على هذا الجهاز؟ (Y/N)
set /p confirm=

if /i "%confirm%"=="Y" (
    echo 📁 إنشاء مجلد التطبيق...
    set "INSTALL_DIR=%USERPROFILE%\Desktop\نظام الرواتب"
    if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

    echo 📦 نسخ ملفات التطبيق...
    copy "PayrollSystem.exe" "%INSTALL_DIR%\"
    copy "دليل_الاستخدام.md" "%INSTALL_DIR%\"

    echo 🔗 إنشاء اختصار على سطح المكتب...
    powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام الرواتب.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\PayrollSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

    echo ✅ تم التثبيت بنجاح!
    echo 🚀 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 📁 مجلد التطبيق: %INSTALL_DIR%
) else (
    echo ❌ تم إلغاء التثبيت
)

echo ============================================================
pause
