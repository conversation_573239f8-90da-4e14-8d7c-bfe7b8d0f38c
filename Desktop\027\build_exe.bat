@echo off
echo ============================================================
echo 🏗️ بناء نظام صرف رواتب العمالة المنزلية - ملف تنفيذي
echo ============================================================

echo 📦 تثبيت المكتبات المطلوبة...
pip install -r requirements.txt

echo 🔧 بناء الملف التنفيذي مع الأيقونة...
pyinstaller --onefile --windowed --name "PayrollSystem" ^
    --icon="static/app_icon.ico" ^
    --add-data "templates;templates" ^
    --add-data "static;static" ^
    --add-data "fonts;fonts" ^
    --add-data "translations.py;." ^
    --add-data "models.py;." ^
    --add-data "forms.py;." ^
    --add-data "config.py;." ^
    --add-data "app.py;." ^
    --hidden-import flask ^
    --hidden-import flask_sqlalchemy ^
    --hidden-import flask_wtf ^
    --hidden-import wtforms ^
    --hidden-import reportlab ^
    --hidden-import openpyxl ^
    --hidden-import arabic_reshaper ^
    --hidden-import bidi ^
    --hidden-import sqlalchemy ^
    --hidden-import werkzeug ^
    --hidden-import jinja2 ^
    --hidden-import markupsafe ^
    --hidden-import click ^
    --hidden-import itsdangerous ^
    run_standalone.py

echo ✅ تم بناء الملف التنفيذي بنجاح!
echo 📁 الملف موجود في: dist\PayrollSystem.exe
echo ============================================================
pause
