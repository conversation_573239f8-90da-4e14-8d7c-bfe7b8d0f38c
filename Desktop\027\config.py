import os
import sys

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'payroll-system-qatar-2025-secret-key'

    # تحديد مسار قاعدة البيانات للتطبيق المستقل
    if getattr(sys, 'frozen', False):
        # إذا كان التطبيق مجمد (exe)
        base_dir = os.path.dirname(sys.executable)
    else:
        # إذا كان يعمل من Python مباشرة
        base_dir = os.path.abspath(os.path.dirname(__file__))

    db_path = os.path.join(base_dir, 'payroll.db')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{db_path}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # تكوين التطبيق - دولة قطر
    APP_NAME = "نظام صرف رواتب العمالة المنزلية - دولة قطر"
    COMPANY_NAME = "مؤسسة الخدمات المنزلية - قطر"
    COUNTRY = "دولة قطر"
    CURRENCY = "ريال قطري"
    CURRENCY_SYMBOL = "ر.ق"

    # إعدادات التقارير
    REPORTS_PER_PAGE = 50

    # إعدادات الأمان
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = 3600  # ساعة واحدة
