import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///payroll.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # تكوين التطبيق
    APP_NAME = "نظام صرف رواتب العمالة المنزلية"
    COMPANY_NAME = "شركة الخدمات المنزلية"
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    
    # إعدادات الأمان
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = 3600  # ساعة واحدة
