#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة created_at في قاعدة البيانات
"""

import sqlite3
from datetime import datetime

def fix_created_at_issue():
    """إصلاح مشكلة created_at للمستخدمين"""
    
    print("🔧 إصلاح مشكلة created_at في قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('payroll.db')
        cursor = conn.cursor()
        
        # فحص المستخدمين الذين لديهم created_at = NULL
        cursor.execute("SELECT id, username FROM user WHERE created_at IS NULL")
        users_without_created_at = cursor.fetchall()
        
        if users_without_created_at:
            print(f"📊 تم العثور على {len(users_without_created_at)} مستخدم بدون تاريخ إنشاء")
            
            current_time = datetime.now()
            
            for user_id, username in users_without_created_at:
                cursor.execute(
                    "UPDATE user SET created_at = ? WHERE id = ?",
                    (current_time, user_id)
                )
                print(f"✅ تم إصلاح created_at للمستخدم: {username}")
            
            conn.commit()
            print("✅ تم إصلاح جميع المستخدمين بنجاح")
        else:
            print("✅ جميع المستخدمين لديهم تاريخ إنشاء صحيح")
        
        # فحص العمال الذين لديهم created_at = NULL
        cursor.execute("SELECT id, name FROM worker WHERE created_at IS NULL")
        workers_without_created_at = cursor.fetchall()
        
        if workers_without_created_at:
            print(f"📊 تم العثور على {len(workers_without_created_at)} عامل بدون تاريخ إنشاء")
            
            current_time = datetime.now()
            
            for worker_id, name in workers_without_created_at:
                cursor.execute(
                    "UPDATE worker SET created_at = ? WHERE id = ?",
                    (current_time, worker_id)
                )
                print(f"✅ تم إصلاح created_at للعامل: {name}")
            
            conn.commit()
            print("✅ تم إصلاح جميع العمال بنجاح")
        else:
            print("✅ جميع العمال لديهم تاريخ إنشاء صحيح")
        
        # فحص الرواتب التي لديها created_at = NULL
        cursor.execute("SELECT id FROM salary WHERE created_at IS NULL")
        salaries_without_created_at = cursor.fetchall()
        
        if salaries_without_created_at:
            print(f"📊 تم العثور على {len(salaries_without_created_at)} راتب بدون تاريخ إنشاء")
            
            current_time = datetime.now()
            
            for (salary_id,) in salaries_without_created_at:
                cursor.execute(
                    "UPDATE salary SET created_at = ? WHERE id = ?",
                    (current_time, salary_id)
                )
            
            conn.commit()
            print("✅ تم إصلاح جميع الرواتب بنجاح")
        else:
            print("✅ جميع الرواتب لديها تاريخ إنشاء صحيح")
        
        conn.close()
        
        print("\n" + "="*60)
        print("✅ تم إصلاح جميع مشاكل created_at بنجاح!")
        print("🔄 يمكنك الآن الوصول لصفحة المستخدمين بدون أخطاء")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح created_at: {e}")
        return False

def verify_fix():
    """التحقق من إصلاح المشكلة"""
    
    print("\n🔍 التحقق من إصلاح المشكلة...")
    
    try:
        conn = sqlite3.connect('payroll.db')
        cursor = conn.cursor()
        
        # فحص المستخدمين
        cursor.execute("SELECT COUNT(*) FROM user WHERE created_at IS NULL")
        null_users = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user")
        total_users = cursor.fetchone()[0]
        
        print(f"👥 المستخدمين: {total_users - null_users}/{total_users} لديهم تاريخ إنشاء صحيح")
        
        # فحص العمال
        cursor.execute("SELECT COUNT(*) FROM worker WHERE created_at IS NULL")
        null_workers = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM worker")
        total_workers = cursor.fetchone()[0]
        
        print(f"👷 العمال: {total_workers - null_workers}/{total_workers} لديهم تاريخ إنشاء صحيح")
        
        # فحص الرواتب
        cursor.execute("SELECT COUNT(*) FROM salary WHERE created_at IS NULL")
        null_salaries = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM salary")
        total_salaries = cursor.fetchone()[0]
        
        print(f"💰 الرواتب: {total_salaries - null_salaries}/{total_salaries} لديها تاريخ إنشاء صحيح")
        
        conn.close()
        
        if null_users == 0 and null_workers == 0 and null_salaries == 0:
            print("✅ جميع البيانات صحيحة!")
            return True
        else:
            print("⚠️ لا تزال هناك بيانات تحتاج إصلاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

if __name__ == "__main__":
    print("🔧 أداة إصلاح مشكلة created_at")
    print("="*60)
    
    if fix_created_at_issue():
        verify_fix()
        
        print("\n💡 الآن يمكنك:")
        print("1. الذهاب إلى صفحة المستخدمين بدون أخطاء")
        print("2. عرض ملفات المستخدمين الشخصية")
        print("3. استخدام جميع ميزات النظام بشكل طبيعي")
    else:
        print("❌ فشل في إصلاح المشكلة")
