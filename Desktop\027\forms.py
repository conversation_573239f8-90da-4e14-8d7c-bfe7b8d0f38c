from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, FloatField, IntegerField, DateField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=4)])
    submit = SubmitField('تسجيل الدخول')

class WorkerForm(FlaskForm):
    """نموذج إضافة/تعديل العامل"""
    name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    nationality = SelectField('الجنسية', choices=[
        ('سعودي', 'سعودي'),
        ('مصري', 'مصري'),
        ('سوداني', 'سوداني'),
        ('فلبيني', 'فلبيني'),
        ('إندونيسي', 'إندونيسي'),
        ('بنغلاديشي', 'بنغلاديشي'),
        ('باكستاني', 'باكستاني'),
        ('هندي', 'هندي'),
        ('سريلانكي', 'سريلانكي'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    
    job_type = SelectField('نوع العمل', choices=[
        ('خادمة منزلية', 'خادمة منزلية'),
        ('طباخة', 'طباخة'),
        ('مربية أطفال', 'مربية أطفال'),
        ('عاملة تنظيف', 'عاملة تنظيف'),
        ('سائق خاص', 'سائق خاص'),
        ('بستاني', 'بستاني'),
        ('حارس', 'حارس'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])
    
    id_number = StringField('رقم الهوية/الإقامة', validators=[DataRequired(), Length(min=10, max=20)])
    phone = StringField('رقم الجوال', validators=[Optional(), Length(max=20)])
    hire_date = DateField('تاريخ التوظيف', validators=[DataRequired()], default=datetime.today)
    basic_salary = FloatField('الراتب الأساسي', validators=[DataRequired(), NumberRange(min=0)])
    
    status = SelectField('الحالة', choices=[
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('terminated', 'منتهي الخدمة')
    ], default='active')
    
    submit = SubmitField('حفظ')

class SalaryForm(FlaskForm):
    """نموذج إضافة/تعديل الراتب"""
    worker_id = SelectField('العامل', coerce=int, validators=[DataRequired()])
    month = SelectField('الشهر', choices=[
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, validators=[DataRequired()])
    
    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)], 
                       default=datetime.now().year)
    
    basic_salary = FloatField('الراتب الأساسي', validators=[DataRequired(), NumberRange(min=0)])
    allowances = FloatField('البدلات', validators=[Optional(), NumberRange(min=0)], default=0)
    deductions = FloatField('الخصومات', validators=[Optional(), NumberRange(min=0)], default=0)
    
    payment_status = SelectField('حالة الدفع', choices=[
        ('unpaid', 'غير مدفوع'),
        ('paid', 'مدفوع')
    ], default='unpaid')
    
    payment_date = DateField('تاريخ الدفع', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('حفظ')

class ReportForm(FlaskForm):
    """نموذج التقارير"""
    month = SelectField('الشهر', choices=[
        (0, 'جميع الشهور'),
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, default=datetime.now().month)
    
    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)], 
                       default=datetime.now().year)
    
    payment_status = SelectField('حالة الدفع', choices=[
        ('all', 'الكل'),
        ('paid', 'مدفوع'),
        ('unpaid', 'غير مدفوع')
    ], default='all')
    
    submit = SubmitField('عرض التقرير')
