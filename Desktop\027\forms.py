from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, SelectField, FloatField, IntegerField, DateField, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, ValidationError
from datetime import datetime

# استيراد Optional بطريقة آمنة
try:
    from wtforms.validators import Optional
except ImportError:
    # في حالة عدم وجود Optional، نستخدم InputRequired بدلاً منه
    class Optional:
        def __call__(self, form, field):
            pass

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=4)])
    submit = SubmitField('تسجيل الدخول')

class WorkerForm(FlaskForm):
    """نموذج إضافة/تعديل العامل"""
    name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    nationality = SelectField('الجنسية', choices=[
        # دول الخليج العربي (أولوية)
        ('قطري', 'قطري'),
        ('سعودي', 'سعودي'),
        ('إماراتي', 'إماراتي'),
        ('كويتي', 'كويتي'),
        ('بحريني', 'بحريني'),
        ('عماني', 'عماني'),

        # الدول العربية
        ('مصري', 'مصري'),
        ('سوداني', 'سوداني'),
        ('مغربي', 'مغربي'),
        ('جزائري', 'جزائري'),
        ('تونسي', 'تونسي'),
        ('ليبي', 'ليبي'),
        ('لبناني', 'لبناني'),
        ('سوري', 'سوري'),
        ('أردني', 'أردني'),
        ('فلسطيني', 'فلسطيني'),
        ('عراقي', 'عراقي'),
        ('يمني', 'يمني'),
        ('موريتاني', 'موريتاني'),
        ('جيبوتي', 'جيبوتي'),
        ('جزر القمر', 'جزر القمر'),

        # دول آسيا (العمالة المنزلية الشائعة)
        ('فلبيني', 'فلبيني'),
        ('إندونيسي', 'إندونيسي'),
        ('بنغلاديشي', 'بنغلاديشي'),
        ('باكستاني', 'باكستاني'),
        ('هندي', 'هندي'),
        ('سريلانكي', 'سريلانكي'),
        ('نيبالي', 'نيبالي'),
        ('تايلاندي', 'تايلاندي'),
        ('فيتنامي', 'فيتنامي'),
        ('كمبودي', 'كمبودي'),
        ('ميانماري', 'ميانماري'),
        ('لاوسي', 'لاوسي'),
        ('ماليزي', 'ماليزي'),
        ('سنغافوري', 'سنغافوري'),
        ('صيني', 'صيني'),
        ('ياباني', 'ياباني'),
        ('كوري جنوبي', 'كوري جنوبي'),
        ('كوري شمالي', 'كوري شمالي'),
        ('تايواني', 'تايواني'),
        ('هونغ كونغي', 'هونغ كونغي'),
        ('مونغولي', 'مونغولي'),
        ('كازاخستاني', 'كازاخستاني'),
        ('أوزبكستاني', 'أوزبكستاني'),
        ('تركمانستاني', 'تركمانستاني'),
        ('طاجيكستاني', 'طاجيكستاني'),
        ('قيرغيزستاني', 'قيرغيزستاني'),
        ('أذربيجاني', 'أذربيجاني'),
        ('أرميني', 'أرميني'),
        ('جورجي', 'جورجي'),
        ('تركي', 'تركي'),
        ('إيراني', 'إيراني'),
        ('أفغاني', 'أفغاني'),
        ('مالديفي', 'مالديفي'),
        ('بوتاني', 'بوتاني'),
        ('برونايي', 'برونايي'),
        ('تيموري', 'تيموري'),

        # دول أفريقيا
        ('إثيوبي', 'إثيوبي'),
        ('إريتري', 'إريتري'),
        ('صومالي', 'صومالي'),
        ('كيني', 'كيني'),
        ('أوغندي', 'أوغندي'),
        ('تنزاني', 'تنزاني'),
        ('رواندي', 'رواندي'),
        ('بوروندي', 'بوروندي'),
        ('جنوب سوداني', 'جنوب سوداني'),
        ('تشادي', 'تشادي'),
        ('أفريقي وسطي', 'أفريقي وسطي'),
        ('كاميروني', 'كاميروني'),
        ('نيجيري', 'نيجيري'),
        ('غاني', 'غاني'),
        ('توغولي', 'توغولي'),
        ('بنيني', 'بنيني'),
        ('بوركينابي', 'بوركينابي'),
        ('مالي', 'مالي'),
        ('نيجري', 'نيجري'),
        ('ساحل عاجي', 'ساحل عاجي'),
        ('ليبيري', 'ليبيري'),
        ('سيراليوني', 'سيراليوني'),
        ('غيني', 'غيني'),
        ('غيني بيساو', 'غيني بيساو'),
        ('غامبي', 'غامبي'),
        ('سنغالي', 'سنغالي'),
        ('موريتاني', 'موريتاني'),
        ('مالاوي', 'مالاوي'),
        ('زامبي', 'زامبي'),
        ('زيمبابوي', 'زيمبابوي'),
        ('بوتسواني', 'بوتسواني'),
        ('ناميبي', 'ناميبي'),
        ('جنوب أفريقي', 'جنوب أفريقي'),
        ('ليسوتو', 'ليسوتو'),
        ('سوازيلاندي', 'سوازيلاندي'),
        ('موزمبيقي', 'موزمبيقي'),
        ('مدغشقري', 'مدغشقري'),
        ('موريشيوسي', 'موريشيوسي'),
        ('سيشيلي', 'سيشيلي'),
        ('جزر القمر', 'جزر القمر'),
        ('رأس الرجاء الأخضر', 'رأس الرجاء الأخضر'),
        ('ساو تومي', 'ساو تومي'),
        ('غيني الاستوائية', 'غيني الاستوائية'),
        ('غابوني', 'غابوني'),
        ('كونغولي', 'كونغولي'),
        ('كونغولي ديمقراطي', 'كونغولي ديمقراطي'),
        ('أنغولي', 'أنغولي'),

        # دول أوروبا
        ('بريطاني', 'بريطاني'),
        ('إيرلندي', 'إيرلندي'),
        ('فرنسي', 'فرنسي'),
        ('ألماني', 'ألماني'),
        ('إيطالي', 'إيطالي'),
        ('إسباني', 'إسباني'),
        ('برتغالي', 'برتغالي'),
        ('هولندي', 'هولندي'),
        ('بلجيكي', 'بلجيكي'),
        ('لوكسمبورغي', 'لوكسمبورغي'),
        ('سويسري', 'سويسري'),
        ('نمساوي', 'نمساوي'),
        ('دنماركي', 'دنماركي'),
        ('سويدي', 'سويدي'),
        ('نرويجي', 'نرويجي'),
        ('فنلندي', 'فنلندي'),
        ('آيسلندي', 'آيسلندي'),
        ('بولندي', 'بولندي'),
        ('تشيكي', 'تشيكي'),
        ('سلوفاكي', 'سلوفاكي'),
        ('هنغاري', 'هنغاري'),
        ('روماني', 'روماني'),
        ('بلغاري', 'بلغاري'),
        ('يوناني', 'يوناني'),
        ('قبرصي', 'قبرصي'),
        ('مالطي', 'مالطي'),
        ('كرواتي', 'كرواتي'),
        ('سلوفيني', 'سلوفيني'),
        ('بوسني', 'بوسني'),
        ('صربي', 'صربي'),
        ('جبل أسود', 'جبل أسود'),
        ('مقدوني', 'مقدوني'),
        ('ألباني', 'ألباني'),
        ('كوسوفي', 'كوسوفي'),
        ('إستوني', 'إستوني'),
        ('لاتفي', 'لاتفي'),
        ('ليتواني', 'ليتواني'),
        ('بيلاروسي', 'بيلاروسي'),
        ('أوكراني', 'أوكراني'),
        ('مولدوفي', 'مولدوفي'),
        ('روسي', 'روسي'),

        # دول أمريكا الشمالية
        ('أمريكي', 'أمريكي'),
        ('كندي', 'كندي'),
        ('مكسيكي', 'مكسيكي'),
        ('غواتيمالي', 'غواتيمالي'),
        ('بليزي', 'بليزي'),
        ('سلفادوري', 'سلفادوري'),
        ('هندوراسي', 'هندوراسي'),
        ('نيكاراغوي', 'نيكاراغوي'),
        ('كوستاريكي', 'كوستاريكي'),
        ('بنمي', 'بنمي'),

        # دول الكاريبي
        ('كوبي', 'كوبي'),
        ('جامايكي', 'جامايكي'),
        ('هايتي', 'هايتي'),
        ('دومينيكاني', 'دومينيكاني'),
        ('بورتوريكي', 'بورتوريكي'),
        ('ترينيداد وتوباغو', 'ترينيداد وتوباغو'),
        ('باربادوسي', 'باربادوسي'),
        ('باهامي', 'باهامي'),

        # دول أمريكا الجنوبية
        ('برازيلي', 'برازيلي'),
        ('أرجنتيني', 'أرجنتيني'),
        ('تشيلي', 'تشيلي'),
        ('بيروفي', 'بيروفي'),
        ('كولومبي', 'كولومبي'),
        ('فنزويلي', 'فنزويلي'),
        ('إكوادوري', 'إكوادوري'),
        ('بوليفي', 'بوليفي'),
        ('باراغوايي', 'باراغوايي'),
        ('أوروغوايي', 'أوروغوايي'),
        ('غياني', 'غياني'),
        ('سورينامي', 'سورينامي'),
        ('غيانا فرنسية', 'غيانا فرنسية'),

        # دول أوقيانوسيا
        ('أسترالي', 'أسترالي'),
        ('نيوزيلندي', 'نيوزيلندي'),
        ('فيجي', 'فيجي'),
        ('بابوا غينيا الجديدة', 'بابوا غينيا الجديدة'),
        ('جزر سليمان', 'جزر سليمان'),
        ('فانواتو', 'فانواتو'),
        ('ساموا', 'ساموا'),
        ('تونغا', 'تونغا'),
        ('كيريباتي', 'كيريباتي'),
        ('توفالو', 'توفالو'),
        ('ناورو', 'ناورو'),
        ('بالاو', 'بالاو'),
        ('مارشال', 'مارشال'),
        ('ميكرونيزيا', 'ميكرونيزيا'),

        # أخرى
        ('عديم الجنسية', 'عديم الجنسية'),
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])

    job_type = SelectField('نوع العمل', choices=[
        # العمالة المنزلية النسائية
        ('خادمة منزلية', 'خادمة منزلية'),
        ('طباخة', 'طباخة'),
        ('مربية أطفال', 'مربية أطفال'),
        ('عاملة تنظيف', 'عاملة تنظيف'),
        ('مدبرة منزل', 'مدبرة منزل'),
        ('عاملة كي وغسيل', 'عاملة كي وغسيل'),
        ('مساعدة مطبخ', 'مساعدة مطبخ'),
        ('عاملة رعاية مسنين', 'عاملة رعاية مسنين'),
        ('عاملة رعاية ذوي الاحتياجات الخاصة', 'عاملة رعاية ذوي الاحتياجات الخاصة'),
        ('مرافقة سيدات', 'مرافقة سيدات'),
        ('عاملة تنظيم وترتيب', 'عاملة تنظيم وترتيب'),

        # العمالة المنزلية الرجالية
        ('سائق خاص', 'سائق خاص'),
        ('بستاني', 'بستاني'),
        ('حارس أمن', 'حارس أمن'),
        ('عامل صيانة عامة', 'عامل صيانة عامة'),
        ('عامل تنظيف خارجي', 'عامل تنظيف خارجي'),
        ('عامل مسابح', 'عامل مسابح'),
        ('عامل كهرباء منزلية', 'عامل كهرباء منزلية'),
        ('عامل سباكة منزلية', 'عامل سباكة منزلية'),
        ('عامل تكييف', 'عامل تكييف'),
        ('عامل نظافة سيارات', 'عامل نظافة سيارات'),
        ('حارس بوابة', 'حارس بوابة'),
        ('عامل حديقة ومناظر طبيعية', 'عامل حديقة ومناظر طبيعية'),

        # العمالة المتخصصة
        ('مدرس خصوصي', 'مدرس خصوصي'),
        ('مدربة رياضة منزلية', 'مدربة رياضة منزلية'),
        ('معالجة طبيعية منزلية', 'معالجة طبيعية منزلية'),
        ('ممرضة منزلية', 'ممرضة منزلية'),
        ('مصففة شعر منزلية', 'مصففة شعر منزلية'),
        ('خياطة منزلية', 'خياطة منزلية'),
        ('عاملة تجميل منزلية', 'عاملة تجميل منزلية'),

        # العمالة الإدارية والمكتبية
        ('سكرتيرة منزلية', 'سكرتيرة منزلية'),
        ('محاسبة منزلية', 'محاسبة منزلية'),
        ('مساعدة إدارية', 'مساعدة إدارية'),

        # العمالة الموسمية والمؤقتة
        ('عاملة مناسبات', 'عاملة مناسبات'),
        ('عامل مناسبات', 'عامل مناسبات'),
        ('عاملة تنظيف مؤقتة', 'عاملة تنظيف مؤقتة'),
        ('عامل صيانة مؤقت', 'عامل صيانة مؤقت'),

        # أخرى
        ('أخرى', 'أخرى')
    ], validators=[DataRequired()])

    id_number = StringField('رقم الهوية/الإقامة/الفيزا', validators=[DataRequired(), Length(min=6, max=25)])
    phone = StringField('رقم الجوال', validators=[Optional(), Length(max=20)])
    hire_date = DateField('تاريخ التوظيف', validators=[DataRequired()], default=datetime.today)
    basic_salary = FloatField('الراتب الأساسي', validators=[DataRequired(), NumberRange(min=0)])

    status = SelectField('الحالة', choices=[
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('terminated', 'منتهي الخدمة')
    ], default='active')

    submit = SubmitField('حفظ')

class SalaryForm(FlaskForm):
    """نموذج إضافة/تعديل الراتب"""
    worker_id = SelectField('العامل', coerce=int, validators=[DataRequired()])
    month = SelectField('الشهر', choices=[
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, validators=[DataRequired()])

    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)],
                       default=datetime.now().year)

    basic_salary = FloatField('الراتب الأساسي', validators=[DataRequired(), NumberRange(min=0)])
    allowances = FloatField('البدلات', validators=[Optional(), NumberRange(min=0)], default=0)
    deductions = FloatField('الخصومات', validators=[Optional(), NumberRange(min=0)], default=0)

    payment_status = SelectField('حالة الدفع', choices=[
        ('unpaid', 'غير مدفوع'),
        ('paid', 'مدفوع')
    ], default='unpaid')

    payment_date = DateField('تاريخ الدفع', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])

    submit = SubmitField('حفظ')

class ReportForm(FlaskForm):
    """نموذج التقارير"""
    month = SelectField('الشهر', choices=[
        (0, 'جميع الشهور'),
        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
    ], coerce=int, default=datetime.now().month)

    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)],
                       default=datetime.now().year)

    payment_status = SelectField('حالة الدفع', choices=[
        ('all', 'الكل'),
        ('paid', 'مدفوع'),
        ('unpaid', 'غير مدفوع')
    ], default='all')

    submit = SubmitField('عرض التقرير')

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل المستخدم"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=80)])
    full_name = StringField('الاسم الكامل', validators=[DataRequired(), Length(min=2, max=100)])
    password = PasswordField('كلمة المرور', validators=[Optional(), Length(min=4)])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[Optional()])

    role = SelectField('الدور', choices=[
        ('admin', 'مدير النظام'),
        ('manager', 'مدير'),
        ('employee', 'موظف'),
        ('viewer', 'مستعرض فقط')
    ], validators=[DataRequired()])

    is_active = SelectField('الحالة', choices=[
        ('True', 'نشط'),
        ('False', 'غير نشط')
    ], coerce=lambda x: x == 'True', default='True')

    submit = SubmitField('حفظ')

    def validate_confirm_password(self, field):
        if self.password.data and field.data != self.password.data:
            raise ValidationError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=4)])
    confirm_password = PasswordField('تأكيد كلمة المرور الجديدة', validators=[DataRequired()])

    submit = SubmitField('تغيير كلمة المرور')

    def validate_confirm_password(self, field):
        if field.data != self.new_password.data:
            raise ValidationError('كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين')

class SystemSettingsForm(FlaskForm):
    """نموذج إعدادات النظام"""
    # إعدادات الشركة
    company_name = StringField('اسم الشركة', validators=[DataRequired(), Length(min=2, max=200)])
    company_address = TextAreaField('عنوان الشركة', validators=[Optional(), Length(max=500)])
    company_phone = StringField('هاتف الشركة', validators=[Optional(), Length(max=20)])
    company_email = StringField('بريد الشركة الإلكتروني', validators=[Optional(), Length(max=100)])
    company_website = StringField('موقع الشركة الإلكتروني', validators=[Optional(), Length(max=100)])

    # إعدادات العملة
    currency_name = StringField('اسم العملة', validators=[DataRequired(), Length(min=1, max=50)])
    currency_symbol = StringField('رمز العملة', validators=[DataRequired(), Length(min=1, max=10)])

    # إعدادات النظام
    system_language = SelectField('لغة النظام', choices=[
        ('ar', 'العربية'),
        ('en', 'English')
    ], default='ar')

    timezone = SelectField('المنطقة الزمنية', choices=[
        ('Asia/Qatar', 'توقيت قطر (GMT+3)'),
        ('Asia/Riyadh', 'توقيت السعودية (GMT+3)'),
        ('Asia/Dubai', 'توقيت الإمارات (GMT+4)'),
        ('Asia/Kuwait', 'توقيت الكويت (GMT+3)')
    ], default='Asia/Qatar')

    date_format = SelectField('تنسيق التاريخ', choices=[
        ('%Y/%m/%d', 'سنة/شهر/يوم (2025/05/29)'),
        ('%d/%m/%Y', 'يوم/شهر/سنة (29/05/2025)'),
        ('%m/%d/%Y', 'شهر/يوم/سنة (05/29/2025)')
    ], default='%Y/%m/%d')

    # إعدادات الشريط المتحرك
    enable_ticker = SelectField('تفعيل الشريط المتحرك', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], default='yes')

    ticker_speed = SelectField('سرعة الشريط المتحرك', choices=[
        ('slow', 'بطيء'),
        ('normal', 'عادي'),
        ('fast', 'سريع')
    ], default='normal')

    ticker_content = SelectField('محتوى الشريط المتحرك', choices=[
        ('workers', 'أسماء العمال'),
        ('company_info', 'معلومات الشركة'),
        ('custom', 'نص مخصص')
    ], default='workers')

    ticker_custom_text = StringField('النص المخصص للشريط', validators=[Optional(), Length(max=500)])

    submit = SubmitField('حفظ الإعدادات')

class ReportSettingsForm(FlaskForm):
    """نموذج إعدادات التقارير"""
    # إعدادات PDF
    pdf_page_size = SelectField('حجم الصفحة', choices=[
        ('A4', 'A4'),
        ('A3', 'A3'),
        ('Letter', 'Letter')
    ], default='A4')

    pdf_orientation = SelectField('اتجاه الصفحة', choices=[
        ('portrait', 'عمودي'),
        ('landscape', 'أفقي')
    ], default='landscape')

    pdf_font_size = SelectField('حجم الخط', choices=[
        ('8', 'صغير (8)'),
        ('10', 'متوسط (10)'),
        ('12', 'كبير (12)'),
        ('14', 'كبير جداً (14)')
    ], default='10')

    # إعدادات المحتوى
    include_company_logo = SelectField('تضمين شعار الشركة', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], default='yes')

    include_summary = SelectField('تضمين ملخص الإحصائيات', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], default='yes')

    include_footer = SelectField('تضمين الملاحظة السفلية', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], default='yes')

    # إعدادات الألوان
    header_color = SelectField('لون رأس الجدول', choices=[
        ('darkblue', 'أزرق داكن'),
        ('darkgreen', 'أخضر داكن'),
        ('darkred', 'أحمر داكن'),
        ('purple', 'بنفسجي'),
        ('black', 'أسود')
    ], default='darkblue')

    submit = SubmitField('حفظ إعدادات التقارير')

class PrintSettingsForm(FlaskForm):
    """نموذج إعدادات الطباعة"""
    # إعدادات الطابعة
    default_printer = StringField('الطابعة الافتراضية', validators=[Optional(), Length(max=100)])

    # إعدادات الإيصالات
    receipt_copies = SelectField('عدد النسخ للإيصالات', choices=[
        ('1', 'نسخة واحدة'),
        ('2', 'نسختان'),
        ('3', 'ثلاث نسخ')
    ], default='1')

    receipt_paper_size = SelectField('حجم ورق الإيصالات', choices=[
        ('A4', 'A4'),
        ('A5', 'A5'),
        ('thermal_80mm', 'حراري 80 مم'),
        ('thermal_58mm', 'حراري 58 مم')
    ], default='A4')

    # إعدادات التقارير
    report_copies = SelectField('عدد النسخ للتقارير', choices=[
        ('1', 'نسخة واحدة'),
        ('2', 'نسختان'),
        ('3', 'ثلاث نسخ')
    ], default='1')

    auto_print_receipts = SelectField('طباعة الإيصالات تلقائياً', choices=[
        ('yes', 'نعم'),
        ('no', 'لا')
    ], default='no')

    # إعدادات الجودة
    print_quality = SelectField('جودة الطباعة', choices=[
        ('draft', 'مسودة (سريع)'),
        ('normal', 'عادي'),
        ('high', 'عالي الجودة')
    ], default='normal')

    # إعدادات الهوامش
    margin_top = SelectField('الهامش العلوي', choices=[
        ('0.5', '0.5 سم'),
        ('1.0', '1.0 سم'),
        ('1.5', '1.5 سم'),
        ('2.0', '2.0 سم')
    ], default='1.0')

    margin_bottom = SelectField('الهامش السفلي', choices=[
        ('0.5', '0.5 سم'),
        ('1.0', '1.0 سم'),
        ('1.5', '1.5 سم'),
        ('2.0', '2.0 سم')
    ], default='1.0')

    margin_left = SelectField('الهامش الأيسر', choices=[
        ('0.5', '0.5 سم'),
        ('1.0', '1.0 سم'),
        ('1.5', '1.5 سم'),
        ('2.0', '2.0 سم')
    ], default='1.0')

    margin_right = SelectField('الهامش الأيمن', choices=[
        ('0.5', '0.5 سم'),
        ('1.0', '1.0 سم'),
        ('1.5', '1.5 سم'),
        ('2.0', '2.0 سم')
    ], default='1.0')

    submit = SubmitField('حفظ إعدادات الطباعة')
