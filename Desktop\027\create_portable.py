#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء نسخة محمولة من نظام صرف رواتب العمالة المنزلية
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_portable_version():
    """إنشاء النسخة المحمولة"""

    # إنشاء مجلد النسخة المحمولة
    portable_dir = Path("PayrollSystem_Portable")
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    portable_dir.mkdir()

    # نسخ الملفات الأساسية
    essential_files = [
        ("dist/PayrollSystem.exe", "PayrollSystem_Portable/PayrollSystem.exe"),
        ("README_STANDALONE.md", "PayrollSystem_Portable/دليل_الاستخدام.md"),
        ("static/portable_icon.ico", "PayrollSystem_Portable/portable_icon.ico"),
        ("static/app_icon.ico", "PayrollSystem_Portable/app_icon.ico"),
    ]

    for src, dst in essential_files:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ تم نسخ: {src}")

    # إنشاء ملف تشغيل محمول
    create_portable_launcher()

    # إنشاء ملف معلومات
    create_info_file()

    # إنشاء ملف ZIP
    create_zip_package()

    print("✅ تم إنشاء النسخة المحمولة بنجاح!")

def create_portable_launcher():
    """إنشاء ملف التشغيل المحمول"""
    launcher_script = '''@echo off
chcp 65001 >nul
title نظام صرف رواتب العمالة المنزلية - نسخة محمولة

echo ============================================================
echo 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 📱 نسخة محمولة - تعمل من أي مكان
echo ============================================================

echo 🚀 بدء تشغيل النظام المحمول...
echo ⏳ يرجى الانتظار 10-30 ثانية...
echo 🌐 سيفتح المتصفح على: http://localhost:5000

start PayrollSystem.exe

echo ✅ تم تشغيل النظام!
echo 🔗 إذا لم يفتح المتصفح: http://localhost:5000
echo 🔑 بيانات الدخول: admin / admin123
echo ============================================================
echo 💾 البيانات محفوظة في نفس المجلد
echo 🔄 يمكن نقل المجلد لأي مكان آخر
echo ============================================================
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''

    with open("PayrollSystem_Portable/تشغيل_النظام.bat", "w", encoding="utf-8") as f:
        f.write(launcher_script)

def create_info_file():
    """إنشاء ملف المعلومات"""
    info_content = '''# 📱 نظام صرف رواتب العمالة المنزلية - نسخة محمولة

## 🎯 مميزات النسخة المحمولة:
- ✅ تعمل من أي مكان (فلاشة، قرص صلب خارجي، مجلد)
- ✅ لا تحتاج تثبيت
- ✅ البيانات محفوظة في نفس المجلد
- ✅ يمكن نقلها بين الأجهزة
- ✅ نسخ احتياطية سهلة

## 🚀 طريقة الاستخدام:
1. فك الضغط عن الملف المضغوط
2. شغل "تشغيل_النظام.bat"
3. انتظر حتى يفتح المتصفح
4. سجل الدخول: admin / admin123

## 💾 إدارة البيانات:
- ملف قاعدة البيانات: payroll.db
- للنسخ الاحتياطي: انسخ الملف
- للاستعادة: استبدل الملف

## 📦 محتويات المجلد:
- PayrollSystem.exe (التطبيق الرئيسي)
- تشغيل_النظام.bat (ملف التشغيل)
- payroll.db (قاعدة البيانات - ستظهر بعد أول تشغيل)
- دليل_الاستخدام.md (دليل مفصل)

## 🌐 الوصول:
- محلي: http://localhost:5000
- من الشبكة: http://[IP]:5000

تاريخ الإنشاء: 29 مايو 2025
الإصدار: 13.0 - Portable Edition
'''

    with open("PayrollSystem_Portable/معلومات_النسخة_المحمولة.md", "w", encoding="utf-8") as f:
        f.write(info_content)

def create_zip_package():
    """إنشاء ملف مضغوط للنسخة المحمولة"""
    zip_filename = "PayrollSystem_Portable_v13.zip"

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk("PayrollSystem_Portable"):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, "PayrollSystem_Portable")
                zipf.write(file_path, arcname)

    print(f"✅ تم إنشاء الملف المضغوط: {zip_filename}")

if __name__ == "__main__":
    create_portable_version()
