@echo off
chcp 65001 >nul
echo ============================================================
echo 📦 تثبيت متطلبات نظام صرف رواتب العمالة المنزلية
echo ============================================================

echo 🔍 التحقق من وجود Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo 📦 تحديث pip...
python -m pip install --upgrade pip

echo 📚 تثبيت المكتبات المطلوبة...
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-WTF==1.1.1
pip install WTForms==3.0.1
pip install Werkzeug==2.3.7
pip install reportlab==4.0.4
pip install openpyxl==3.1.2
pip install python-dateutil==2.8.2
pip install arabic-reshaper==3.0.0
pip install python-bidi==0.4.2
pip install Jinja2==3.1.2
pip install MarkupSafe==2.1.3
pip install click==8.1.7
pip install itsdangerous==2.1.2
pip install SQLAlchemy==2.0.21
pip install PyInstaller==5.13.2

echo ✅ تم تثبيت جميع المكتبات بنجاح!
echo ============================================================
echo 🚀 يمكنك الآن تشغيل النظام باستخدام: python run.py
echo 🏗️ أو بناء ملف exe باستخدام: build_exe.bat
echo ============================================================
pause
