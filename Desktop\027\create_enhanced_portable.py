#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إنشاء نسخة محمولة محسنة مع إصلاح مشاكل التشغيل
"""

import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
from werkzeug.security import generate_password_hash

def create_enhanced_portable():
    """إنشاء نسخة محمولة محسنة"""
    
    print("🚀 إنشاء نسخة محمولة محسنة")
    print("=" * 60)
    
    # 1. التأكد من وجود الملف التنفيذي
    if not os.path.exists("dist/PayrollSystem.exe"):
        print("❌ الملف التنفيذي غير موجود، سيتم إنشاؤه...")
        build_exe()
    
    # 2. إنشاء مجلد النسخة المحمولة
    portable_dir = "PayrollSystem_Portable_Enhanced"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir, exist_ok=True)
    print(f"📁 تم إنشاء مجلد: {portable_dir}")
    
    # 3. نسخ الملف التنفيذي
    shutil.copy2("dist/PayrollSystem.exe", f"{portable_dir}/PayrollSystem.exe")
    print("✅ تم نسخ الملف التنفيذي")
    
    # 4. إنشاء قاعدة بيانات مُحسنة
    create_enhanced_database(f"{portable_dir}/payroll.db")
    
    # 5. نسخ الأيقونات
    icon_files = [
        ("static/app_icon.ico", f"{portable_dir}/app_icon.ico"),
        ("static/portable_icon.ico", f"{portable_dir}/portable_icon.ico"),
    ]
    
    for src, dst in icon_files:
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ تم نسخ: {os.path.basename(src)}")
    
    # 6. إنشاء ملفات التشغيل المحسنة
    create_enhanced_batch_files(portable_dir)
    
    # 7. إنشاء دليل الاستخدام المحسن
    create_enhanced_guide(portable_dir)
    
    # 8. إنشاء ملف مضغوط
    create_zip_file(portable_dir)
    
    print("\n" + "="*60)
    print("✅ تم إنشاء النسخة المحمولة المحسنة بنجاح!")
    print(f"📁 المجلد: {portable_dir}")
    print("📦 الملف المضغوط: PayrollSystem_Portable_Enhanced.zip")
    print("🔑 بيانات الدخول: admin / admin123")
    print("="*60)
    
    return True

def build_exe():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    cmd = '''python -m PyInstaller --onefile --name "PayrollSystem" --icon="static/app_icon.ico" --add-data "templates;templates" --add-data "static;static" --add-data "translations.py;." --add-data "models.py;." --add-data "forms.py;." --add-data "config.py;." --add-data "app.py;." run_standalone.py'''
    
    os.system(cmd)
    print("✅ تم بناء الملف التنفيذي")

def create_enhanced_database(db_path):
    """إنشاء قاعدة بيانات محسنة"""
    
    print("🗄️ إنشاء قاعدة بيانات محسنة...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول العمال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS worker (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                nationality VARCHAR(50) NOT NULL,
                job_type VARCHAR(50) NOT NULL,
                passport_number VARCHAR(50),
                visa_number VARCHAR(50),
                phone VARCHAR(20),
                salary DECIMAL(10,2) NOT NULL,
                hire_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول الرواتب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                allowances DECIMAL(10,2) DEFAULT 0,
                deductions DECIMAL(10,2) DEFAULT 0,
                total_salary DECIMAL(10,2) NOT NULL,
                payment_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES worker (id)
            )
        ''')
        
        # إنشاء المستخدمين الافتراضيين
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # المستخدم الرئيسي
        admin_password = generate_password_hash('admin123')
        cursor.execute('''
            INSERT OR REPLACE INTO user (id, username, password_hash, full_name, role, is_active, created_at)
            VALUES (1, ?, ?, ?, ?, ?, ?)
        ''', ('admin', admin_password, 'مدير النظام', 'admin', 1, current_time))
        
        # المستخدم التجريبي
        test_password = generate_password_hash('test123')
        cursor.execute('''
            INSERT OR REPLACE INTO user (id, username, password_hash, full_name, role, is_active, created_at)
            VALUES (2, ?, ?, ?, ?, ?, ?)
        ''', ('test', test_password, 'مستخدم تجريبي', 'user', 1, current_time))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات مع المستخدمين")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def create_enhanced_batch_files(portable_dir):
    """إنشاء ملفات التشغيل المحسنة"""
    
    # ملف التشغيل الرئيسي
    main_batch = f'''@echo off
chcp 65001 >nul
title نظام صرف رواتب العمالة المنزلية - نسخة محمولة محسنة

cls
echo.
echo ============================================================
echo 🏠 نظام صرف رواتب العمالة المنزلية - دولة قطر
echo ============================================================
echo 📱 النسخة المحمولة المحسنة - الإصدار 13.1
echo ============================================================
echo.

echo 🔍 فحص النظام...

REM فحص وجود الملف التنفيذي
if not exist "PayrollSystem.exe" (
    echo ❌ الملف التنفيذي غير موجود!
    echo 💡 تأكد من وجود PayrollSystem.exe في نفس المجلد
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود

REM فحص وجود قاعدة البيانات
if not exist "payroll.db" (
    echo ⚠️ قاعدة البيانات غير موجودة، سيتم إنشاؤها تلقائياً
)

echo.
echo 🚀 بدء تشغيل النظام...
echo ⏳ يرجى الانتظار 15-30 ثانية...
echo 🌐 سيفتح المتصفح على: http://localhost:5000
echo.

REM تشغيل النظام
start "" "PayrollSystem.exe"

REM انتظار تحميل النظام
echo ⏳ انتظار تحميل النظام...
timeout /t 20 /nobreak >nul

REM فتح المتصفح
echo 🌐 فتح المتصفح...
start "" "http://localhost:5000"

echo.
echo ============================================================
echo ✅ تم تشغيل النظام بنجاح!
echo 🔗 الرابط: http://localhost:5000
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo ============================================================
echo 💾 البيانات محفوظة في نفس المجلد
echo 🔄 يمكن نقل المجلد لأي مكان آخر
echo 🛑 لإيقاف النظام: أغلق نافذة المتصفح ثم اضغط Ctrl+C
echo ============================================================
echo.
echo اضغط أي مفتاح لإغلاق هذه النافذة...
pause >nul
'''
    
    with open(f"{portable_dir}/تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
        f.write(main_batch)
    
    # ملف التشغيل السريع
    quick_batch = f'''@echo off
start "" "PayrollSystem.exe"
timeout /t 15 /nobreak >nul
start "" "http://localhost:5000"
'''
    
    with open(f"{portable_dir}/تشغيل_سريع.bat", 'w', encoding='utf-8') as f:
        f.write(quick_batch)
    
    print("✅ تم إنشاء ملفات التشغيل المحسنة")

def create_enhanced_guide(portable_dir):
    """إنشاء دليل الاستخدام المحسن"""
    
    guide_content = '''# 📱 نظام صرف رواتب العمالة المنزلية - النسخة المحمولة المحسنة

## 🎯 **الإصدار 13.1 - Professional Portable Enhanced Edition**

---

## 🚀 **طرق التشغيل:**

### ⚡ **التشغيل السريع:**
1. **انقر نقراً مزدوجاً** على `تشغيل_سريع.bat`
2. **انتظر 15 ثانية** حتى يفتح المتصفح
3. **سجل الدخول:** `admin` / `admin123`

### 🔧 **التشغيل المفصل:**
1. **انقر نقراً مزدوجاً** على `تشغيل_النظام.bat`
2. **اتبع التعليمات** على الشاشة
3. **سيفتح المتصفح** تلقائياً

### 💻 **التشغيل المباشر:**
1. **انقر نقراً مزدوجاً** على `PayrollSystem.exe`
2. **انتظر 20 ثانية**
3. **اذهب يدوياً** إلى: `http://localhost:5000`

---

## 🔑 **بيانات الدخول:**

```
🌐 الرابط: http://localhost:5000
👤 اسم المستخدم: admin
🔒 كلمة المرور: admin123

👤 مستخدم تجريبي: test
🔒 كلمة المرور: test123
```

---

## ✅ **مميزات النسخة المحسنة:**

### 🎯 **تحسينات التشغيل:**
- ✅ **ملفات تشغيل متعددة** للمرونة
- ✅ **فحص تلقائي** للملفات المطلوبة
- ✅ **رسائل واضحة** لحالة النظام
- ✅ **فتح المتصفح تلقائياً**

### 🇶🇦 **مخصصة لقطر:**
- ✅ **200+ جنسية** شاملة
- ✅ **35+ نوع عمل** للعمالة المنزلية
- ✅ **الريال القطري** كعملة أساسية
- ✅ **التقويم الهجري والميلادي**

### 📊 **وظائف متكاملة:**
- ✅ **إدارة العمال** الكاملة
- ✅ **حساب الرواتب** التلقائي
- ✅ **إيصالات A5** للطباعة
- ✅ **تقارير PDF وExcel**
- ✅ **واجهة عربية** متجاوبة

---

## 💡 **نصائح للاستخدام الأمثل:**

### ✅ **قبل التشغيل:**
1. **تأكد من وجود جميع الملفات** في نفس المجلد
2. **أضف المجلد للاستثناءات** في برنامج الحماية
3. **أغلق البرامج** التي تستخدم المنفذ 5000

### 🔄 **أثناء الاستخدام:**
1. **لا تحذف أي ملف** من المجلد
2. **احتفظ بنسخة احتياطية** من المجلد
3. **أغلق النظام بشكل صحيح** من المتصفح

---

## 🆘 **حل المشاكل:**

### ❌ **النظام لا يفتح:**
1. جرب `تشغيل_النظام.bat` بدلاً من الملف التنفيذي
2. تأكد من عدم حجب برنامج الحماية للملف
3. شغل كمدير (Run as Administrator)

### 🌐 **لا يمكن الوصول للموقع:**
1. انتظر 30 ثانية بعد التشغيل
2. جرب الرابط يدوياً: `http://localhost:5000`
3. تأكد من عدم استخدام المنفذ 5000

### 🔐 **مشكلة في تسجيل الدخول:**
1. تأكد من البيانات: `admin` / `admin123`
2. تأكد من عدم وجود مسافات إضافية
3. جرب المستخدم التجريبي: `test` / `test123`

---

## 📁 **محتويات المجلد:**

```
📁 PayrollSystem_Portable_Enhanced/
├── 🚀 PayrollSystem.exe (الملف الرئيسي)
├── ⚡ تشغيل_سريع.bat (تشغيل سريع)
├── 🔧 تشغيل_النظام.bat (تشغيل مفصل)
├── 🗄️ payroll.db (قاعدة البيانات)
├── 📖 دليل_الاستخدام_المحسن.md (هذا الدليل)
├── 🎨 app_icon.ico (أيقونة التطبيق)
└── 📱 portable_icon.ico (أيقونة النسخة المحمولة)
```

---

## 🎉 **استمتع بالنظام!**

### 🚀 **ابدأ الآن:**
1. **شغل** `تشغيل_سريع.bat`
2. **سجل الدخول** بـ `admin` / `admin123`
3. **استمتع** بالنظام المتكامل!

**النسخة:** 13.1 Professional Portable Enhanced Edition  
**التاريخ:** 29 مايو 2025  
**مخصص لدولة قطر** 🇶🇦

---

## 🇶🇦 نظام صرف رواتب العمالة المنزلية - دولة قطر
### 📱 النسخة المحمولة المحسنة - جاهزة للاستخدام!

**مبروك! النسخة المحمولة المحسنة جاهزة!** 🚀📱💼
'''
    
    with open(f"{portable_dir}/دليل_الاستخدام_المحسن.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ تم إنشاء دليل الاستخدام المحسن")

def create_zip_file(portable_dir):
    """إنشاء ملف مضغوط"""
    
    zip_name = "PayrollSystem_Portable_Enhanced.zip"
    
    print(f"📦 إنشاء الملف المضغوط: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, portable_dir)
                zipf.write(file_path, arc_name)
    
    print(f"✅ تم إنشاء الملف المضغوط: {zip_name}")

if __name__ == "__main__":
    create_enhanced_portable()
    
    print("\n💡 للاستخدام:")
    print("1. فك الضغط عن PayrollSystem_Portable_Enhanced.zip")
    print("2. انقر على تشغيل_سريع.bat")
    print("3. سجل الدخول: admin / admin123")
    print("4. استمتع بالنظام!")
