#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تشخيص وإصلاح خطأ Internal Server Error
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime
from werkzeug.security import generate_password_hash

def diagnose_internal_server_error():
    """تشخيص خطأ Internal Server Error"""
    
    print("🔍 تشخيص خطأ Internal Server Error")
    print("=" * 60)
    
    errors_found = []
    
    # 1. فحص ملفات Python الأساسية
    print("1️⃣ فحص ملفات Python الأساسية...")
    
    required_files = [
        'app.py',
        'models.py',
        'forms.py',
        'config.py',
        'translations.py',
        'run.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} موجود")
            # فحص الأخطاء النحوية
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, file, 'exec')
                print(f"✅ {file} لا يحتوي على أخطاء نحوية")
            except SyntaxError as e:
                error_msg = f"❌ خطأ نحوي في {file}: {e}"
                print(error_msg)
                errors_found.append(error_msg)
            except Exception as e:
                error_msg = f"⚠️ مشكلة في {file}: {e}"
                print(error_msg)
                errors_found.append(error_msg)
        else:
            error_msg = f"❌ {file} غير موجود"
            print(error_msg)
            errors_found.append(error_msg)
    
    # 2. فحص قاعدة البيانات
    print("\n2️⃣ فحص قاعدة البيانات...")
    
    db_files = ['payroll.db', 'instance/payroll.db']
    db_working = False
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ {db_file} موجود")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # فحص الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                required_tables = ['user', 'worker', 'salary']
                for table in required_tables:
                    if any(table in t[0] for t in tables):
                        print(f"✅ جدول {table} موجود")
                    else:
                        error_msg = f"❌ جدول {table} غير موجود"
                        print(error_msg)
                        errors_found.append(error_msg)
                
                # فحص المستخدمين
                try:
                    cursor.execute("SELECT COUNT(*) FROM user")
                    user_count = cursor.fetchone()[0]
                    print(f"✅ عدد المستخدمين: {user_count}")
                    
                    if user_count == 0:
                        error_msg = "⚠️ لا يوجد مستخدمين في قاعدة البيانات"
                        print(error_msg)
                        errors_found.append(error_msg)
                    
                    db_working = True
                    
                except Exception as e:
                    error_msg = f"❌ خطأ في قراءة جدول المستخدمين: {e}"
                    print(error_msg)
                    errors_found.append(error_msg)
                
                conn.close()
                break
                
            except Exception as e:
                error_msg = f"❌ خطأ في الاتصال بقاعدة البيانات {db_file}: {e}"
                print(error_msg)
                errors_found.append(error_msg)
        else:
            print(f"⚠️ {db_file} غير موجود")
    
    if not db_working:
        error_msg = "❌ لا توجد قاعدة بيانات تعمل"
        print(error_msg)
        errors_found.append(error_msg)
    
    # 3. فحص مجلد القوالب
    print("\n3️⃣ فحص مجلد القوالب...")
    
    if os.path.exists('templates'):
        print("✅ مجلد templates موجود")
        
        required_templates = [
            'base.html',
            'login.html',
            'dashboard.html',
            'workers.html',
            'salaries.html',
            'users.html'
        ]
        
        for template in required_templates:
            template_path = f'templates/{template}'
            if os.path.exists(template_path):
                print(f"✅ {template} موجود")
            else:
                error_msg = f"❌ {template} غير موجود"
                print(error_msg)
                errors_found.append(error_msg)
    else:
        error_msg = "❌ مجلد templates غير موجود"
        print(error_msg)
        errors_found.append(error_msg)
    
    # 4. فحص مجلد الملفات الثابتة
    print("\n4️⃣ فحص مجلد الملفات الثابتة...")
    
    if os.path.exists('static'):
        print("✅ مجلد static موجود")
    else:
        error_msg = "❌ مجلد static غير موجود"
        print(error_msg)
        errors_found.append(error_msg)
    
    # 5. فحص المتطلبات
    print("\n5️⃣ فحص المتطلبات...")
    
    try:
        import flask
        print(f"✅ Flask مثبت - الإصدار: {flask.__version__}")
    except ImportError:
        error_msg = "❌ Flask غير مثبت"
        print(error_msg)
        errors_found.append(error_msg)
    
    try:
        import flask_sqlalchemy
        print(f"✅ Flask-SQLAlchemy مثبت")
    except ImportError:
        error_msg = "❌ Flask-SQLAlchemy غير مثبت"
        print(error_msg)
        errors_found.append(error_msg)
    
    try:
        import flask_wtf
        print(f"✅ Flask-WTF مثبت")
    except ImportError:
        error_msg = "❌ Flask-WTF غير مثبت"
        print(error_msg)
        errors_found.append(error_msg)
    
    # 6. اختبار تشغيل التطبيق
    print("\n6️⃣ اختبار تشغيل التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, os.getcwd())
        
        import app as flask_app
        print("✅ تم استيراد التطبيق بنجاح")
        
        # اختبار إنشاء التطبيق
        test_app = flask_app.app
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار السياق
        with test_app.app_context():
            print("✅ سياق التطبيق يعمل بشكل صحيح")
        
    except Exception as e:
        error_msg = f"❌ خطأ في تشغيل التطبيق: {e}"
        print(error_msg)
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        errors_found.append(error_msg)
    
    # 7. تقرير النتائج
    print("\n" + "="*60)
    print("📊 تقرير التشخيص:")
    print("="*60)
    
    if not errors_found:
        print("✅ لم يتم العثور على أخطاء واضحة")
        print("💡 قد تكون المشكلة في:")
        print("   - إعدادات الخادم")
        print("   - متغيرات البيئة")
        print("   - صلاحيات الملفات")
        print("   - تضارب في المنافذ")
    else:
        print(f"❌ تم العثور على {len(errors_found)} مشكلة:")
        for i, error in enumerate(errors_found, 1):
            print(f"   {i}. {error}")
    
    return errors_found

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    
    print("\n🔧 إصلاح المشاكل الشائعة...")
    
    # 1. إنشاء قاعدة بيانات جديدة
    print("1️⃣ إنشاء قاعدة بيانات جديدة...")
    
    try:
        # إنشاء مجلد instance إذا لم يكن موجود
        os.makedirs('instance', exist_ok=True)
        
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('instance/payroll.db')
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول العمال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS worker (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                nationality VARCHAR(50) NOT NULL,
                job_type VARCHAR(50) NOT NULL,
                passport_number VARCHAR(50),
                visa_number VARCHAR(50),
                phone VARCHAR(20),
                salary DECIMAL(10,2) NOT NULL,
                hire_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول الرواتب
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS salary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                worker_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary DECIMAL(10,2) NOT NULL,
                allowances DECIMAL(10,2) DEFAULT 0,
                deductions DECIMAL(10,2) DEFAULT 0,
                total_salary DECIMAL(10,2) NOT NULL,
                payment_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (worker_id) REFERENCES worker (id)
            )
        ''')
        
        # إنشاء المستخدم الافتراضي
        admin_password = generate_password_hash('admin123')
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute('''
            INSERT OR REPLACE INTO user (id, username, password_hash, full_name, role, is_active, created_at)
            VALUES (1, ?, ?, ?, ?, ?, ?)
        ''', ('admin', admin_password, 'مدير النظام', 'admin', 1, current_time))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    # 2. إنشاء ملف تكوين آمن
    print("2️⃣ إنشاء ملف تكوين آمن...")
    
    safe_config = '''import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/payroll.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # إعدادات اللغة
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    
    # إعدادات التطبيق
    APP_NAME = 'نظام صرف رواتب العمالة المنزلية'
    APP_VERSION = '13.1'
    
    # إعدادات الأمان
    SESSION_COOKIE_SECURE = False  # True في الإنتاج
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
'''
    
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(safe_config)
        print("✅ تم إنشاء ملف التكوين الآمن")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف التكوين: {e}")
    
    print("✅ تم إصلاح المشاكل الشائعة")

def create_error_handler():
    """إنشاء معالج أخطاء محسن"""
    
    error_handler_code = '''
# إضافة معالجات الأخطاء المحسنة إلى app.py

@app.errorhandler(500)
def internal_server_error(error):
    """معالج خطأ الخادم الداخلي"""
    import traceback
    
    # تسجيل الخطأ
    app.logger.error(f'Internal Server Error: {error}')
    app.logger.error(f'Traceback: {traceback.format_exc()}')
    
    # إرجاع صفحة خطأ مخصصة
    return render_template('error.html', 
                         error_code=500,
                         error_message='خطأ داخلي في الخادم',
                         error_details='حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'), 500

@app.errorhandler(404)
def not_found_error(error):
    """معالج خطأ الصفحة غير موجودة"""
    return render_template('error.html',
                         error_code=404,
                         error_message='الصفحة غير موجودة',
                         error_details='الصفحة التي تبحث عنها غير موجودة.'), 404

@app.errorhandler(403)
def forbidden_error(error):
    """معالج خطأ الوصول مرفوض"""
    return render_template('error.html',
                         error_code=403,
                         error_message='الوصول مرفوض',
                         error_details='ليس لديك صلاحية للوصول إلى هذه الصفحة.'), 403
'''
    
    print("💡 أضف هذا الكود إلى ملف app.py لمعالجة الأخطاء بشكل أفضل:")
    print(error_handler_code)

if __name__ == "__main__":
    errors = diagnose_internal_server_error()
    
    if errors:
        print(f"\n🔧 تم العثور على {len(errors)} مشكلة. سيتم إصلاح المشاكل الشائعة...")
        fix_common_issues()
    
    create_error_handler()
    
    print("\n💡 خطوات إضافية لحل المشكلة:")
    print("1. أعد تشغيل النظام: python run.py")
    print("2. تحقق من رسائل الخطأ في terminal")
    print("3. جرب الوصول للموقع مرة أخرى")
    print("4. إذا استمرت المشكلة، تحقق من ملفات السجل")
